// Utility functions for the HR Performance Evaluation System
// Grade Calculation Updated: v4.0 - New Thresholds Applied

// Format date to YYYY-MM-DD
function formatDate(date) {
    const d = new Date(date);
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const year = d.getFullYear();
    return `${year}-${month}-${day}`;
}

// Format date to readable format (e.g., Jan 1, 2023)
function formatReadableDate(date) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(date).toLocaleDateString(undefined, options);
}

// Calculate weighted score
function calculateWeightedScore(score, weight) {
    return (score / 5) * weight;
}

// Calculate total score from individual scores
function calculateTotalScore(scores) {
    if (!scores || scores.length === 0) return 0;
    
    const totalWeight = scores.reduce((sum, item) => sum + item.weight, 0);
    const weightedSum = scores.reduce((sum, item) => sum + calculateWeightedScore(item.score, item.weight), 0);
    
    return totalWeight > 0 ? (weightedSum / totalWeight) * 100 : 0;
}

// Get grade based on score percentage
function getGrade(scorePercentage) {
    if (scorePercentage < 60) return 'Poor';
    if (scorePercentage < 70) return 'Need Improvement';
    if (scorePercentage < 80) return 'Meet Requirements';
    if (scorePercentage < 90) return 'Very Good';
    return 'Excellent';
}

// Get color based on grade
function getGradeColor(grade) {
    switch (grade) {
        case 'Poor': return '#dc3545'; // Red
        case 'Need Improvement': return '#fd7e14'; // Orange
        case 'Meet Requirements': return '#198754'; // Green
        case 'Meets Requirements': return '#198754'; // Green (legacy compatibility)
        case 'Very Good': return '#0d6efd'; // Blue
        case 'Excellent': return '#ffc107'; // Yellow
        case 'Good': return '#0d6efd'; // Blue - treat as "Very Good" (data inconsistency fix)
        default: return '#6c757d'; // Gray
    }
}

// Toggle dark/light mode
function toggleDarkMode() {
    const isDarkMode = document.body.classList.toggle('dark-mode');
    localStorage.setItem('darkMode', isDarkMode);
    
    // Update dark mode toggle button text/icon if it exists
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        if (isDarkMode) {
            darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            darkModeToggle.setAttribute('title', 'Switch to Light Mode');
        } else {
            darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            darkModeToggle.setAttribute('title', 'Switch to Dark Mode');
        }
    }
}

// Apply dark mode based on saved preference
function applyDarkMode() {
    const isDarkMode = localStorage.getItem('darkMode') === 'true';
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        
        // Update dark mode toggle button if it exists
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            darkModeToggle.setAttribute('title', 'Switch to Light Mode');
        }
    }
}

// Export data to Excel
function exportToExcel(tableId, filename = 'data') {
    const table = document.getElementById(tableId);
    if (!table) {
        console.error(`Table with ID ${tableId} not found`);
        return;
    }
    
    // Create a workbook
    const wb = XLSX.utils.book_new();
    
    // Convert table to worksheet
    const ws = XLSX.utils.table_to_sheet(table);
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    
    // Generate Excel file and trigger download
    XLSX.writeFile(wb, `${filename}.xlsx`);
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Hide and remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Validate form inputs
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('input, select, textarea');
    let isValid = true;
    
    inputs.forEach(input => {
        if (input.hasAttribute('required') && !input.value.trim()) {
            input.classList.add('invalid');
            isValid = false;
        } else {
            input.classList.remove('invalid');
        }
    });
    
    return isValid;
}

// Get URL parameters
function getUrlParams() {
    const params = {};
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);
    
    for (const [key, value] of urlParams.entries()) {
        params[key] = value;
    }
    
    return params;
}

// Format number with commas
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Truncate text with ellipsis
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// Enhanced navigation functionality
function initializeNavigation() {
    // Add click handlers to all navigation links
    document.addEventListener('DOMContentLoaded', function() {
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            // Add temporary press effect on mousedown (only for non-current pages)
            link.addEventListener('mousedown', function() {
                if (!this.classList.contains('current-page')) {
                    this.classList.add('pressed');
                }
            });

            // Remove temporary press effect on mouseup
            link.addEventListener('mouseup', function() {
                if (!this.classList.contains('current-page')) {
                    this.classList.remove('pressed');
                }
            });

            // Remove temporary press effect if mouse leaves while pressed
            link.addEventListener('mouseleave', function() {
                if (!this.classList.contains('current-page')) {
                    this.classList.remove('pressed');
                }
            });

            // Handle click navigation - PERSISTENT current page indicator
            link.addEventListener('click', function(e) {
                // Remove current-page class from all nav links
                navLinks.forEach(navLink => {
                    navLink.classList.remove('current-page');
                    navLink.classList.remove('pressed');
                });

                // Add current-page class to clicked link (this stays persistent)
                this.classList.add('current-page');

                console.log('Navigation: Set current page to', this.textContent.trim());
            });
        });

        // Set current page indicator based on current URL
        setCurrentPageIndicator();
    });
}

// Set current page indicator based on URL
function setCurrentPageIndicator() {
    const currentPath = window.location.pathname;
    const currentPage = currentPath.split('/').pop() || 'index.html';

    console.log('Setting current page indicator for:', currentPage);

    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        // Remove any existing current-page classes first
        link.classList.remove('current-page');

        const linkHref = link.getAttribute('href');
        if (linkHref) {
            const linkPage = linkHref.split('/').pop();

            // Match exact page names or handle index page
            if (linkPage === currentPage ||
                (currentPage === 'index.html' && linkPage === 'index.html') ||
                (currentPage === '' && linkPage === 'index.html')) {
                link.classList.add('current-page');
                console.log('Current page set for:', link.textContent.trim());
            }
        }
    });
}

// Extract first name from full name
function getFirstName(fullName) {
    if (!fullName) return '';
    return fullName.trim().split(' ')[0];
}

// Extract first and second name from full name
function getFirstAndSecondName(fullName) {
    if (!fullName) return '';
    const names = fullName.trim().split(' ');
    if (names.length >= 2) {
        return `${names[0]} ${names[1]}`;
    }
    return names[0] || '';
}

// Generate welcome message based on user role and name
function getWelcomeMessage(user) {
    if (!user) return 'Welcome!';

    const role = user.role || '';
    const name = user.name || user.username || '';
    const displayName = getFirstAndSecondName(name);

    switch (role.toLowerCase()) {
        case 'admin':
            return 'Welcome! You are logged in as Admin';
        case 'manager':
            return `Welcome Manager ${displayName}!`;
        case 'employee':
            return `Welcome ${displayName}!`;
        default:
            return displayName ? `Welcome ${displayName}!` : 'Welcome!';
    }
}

// Update user display name to show first and second name
function updateUserDisplayName(elementId, user) {
    const element = document.getElementById(elementId);
    if (!element || !user) return;

    const role = user.role || '';
    const name = user.name || user.username || '';

    if (role.toLowerCase() === 'admin') {
        element.textContent = 'Admin';
    } else {
        const displayName = getFirstAndSecondName(name);
        element.textContent = displayName || name || 'User';
    }
}

// Show welcome notification (only once per session)
function showWelcomeMessage(user) {
    // Check if welcome message was already shown this session
    const welcomeShown = sessionStorage.getItem('welcomeMessageShown');
    if (welcomeShown) {
        return; // Don't show again
    }

    const message = getWelcomeMessage(user);
    showNotification(message, 'success', 3000);

    // Mark welcome message as shown for this session
    sessionStorage.setItem('welcomeMessageShown', 'true');
}

// Export the utility functions
window.appUtils = {
    formatDate,
    formatReadableDate,
    calculateWeightedScore,
    calculateTotalScore,
    getGrade,
    getGradeColor,
    toggleDarkMode,
    applyDarkMode,
    exportToExcel,
    showNotification,
    validateForm,
    getUrlParams,
    formatNumber,
    truncateText,
    initializeNavigation,
    setCurrentPageIndicator,
    getFirstName,
    getFirstAndSecondName,
    getWelcomeMessage,
    updateUserDisplayName,
    showWelcomeMessage
};