<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Performance Evaluation System</title>

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="icon" type="image/png" sizes="96x96" href="assets/icons/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" href="assets/icons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="assets/icons/web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="assets/icons/web-app-manifest-512x512.png">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="msapplication-config" content="assets/icons/browserconfig.xml">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/styles.css">

    <style>
        /* Enhanced login page styling */
        body {
            background: linear-gradient(135deg, #0d6efd 0%, #0056b3 25%, #003d82 50%, #002752 75%, #0d6efd 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 80vh;
            padding: 20px;
            padding-top: 5vh;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 450px;
            width: 100%;
            text-align: center;
            transform: translateY(0);
            transition: transform 0.3s ease;
        }

        .login-card:hover {
            transform: translateY(-5px);
        }

        .login-logo {
            margin-bottom: 20px;
        }

        .login-logo i {
            background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 4rem !important;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            filter: drop-shadow(0 4px 8px rgba(13, 110, 253, 0.3));
        }

        .login-title {
            background: linear-gradient(135deg, #0d6efd 0%, #0056b3 50%, #003d82 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e1e5e9;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
            background: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
            border: none;
            border-radius: 12px;
            padding: 14px 28px;
            font-size: 16px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(13, 110, 253, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(13, 110, 253, 0.6);
            background: linear-gradient(135deg, #0056b3 0%, #003d82 100%);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        /* Dark mode adjustments */
        body.dark-mode {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #2c3e50 50%, #34495e 75%, #2c3e50 100%);
        }

        body.dark-mode .login-card {
            background: rgba(44, 62, 80, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .form-label {
            color: #ecf0f1;
        }

        body.dark-mode .form-control {
            background: rgba(52, 73, 94, 0.8);
            border-color: #34495e;
            color: #ecf0f1;
        }

        body.dark-mode .form-control:focus {
            background: rgba(52, 73, 94, 0.9);
            border-color: #0d6efd;
        }

        .dark-mode-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(13, 110, 253, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(13, 110, 253, 0.5);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(13, 110, 253, 0.2);
        }

        .dark-mode-toggle:hover {
            background: rgba(13, 110, 253, 0.5);
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4);
        }

        /* Dark mode toggle styling for dark mode */
        body.dark-mode .dark-mode-toggle {
            background: rgba(44, 62, 80, 0.8);
            border: 1px solid rgba(52, 73, 94, 0.8);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        body.dark-mode .dark-mode-toggle:hover {
            background: rgba(52, 73, 94, 0.9);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-logo">
                <i class="fas fa-chart-line"></i>
            </div>
            <h1 class="login-title">Performance Evaluation System</h1>
            
            <form id="loginForm" class="mt-4">
                <div class="form-group">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" id="username" class="form-control" placeholder="Enter your username" required>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" id="password" class="form-control" placeholder="Enter your password" required>
                </div>
                
                <div id="loginError" class="alert alert-danger mt-3" style="display: none;"></div>
                
                <button type="submit" class="btn btn-primary w-100 mt-4">Login</button>
            </form>
            

        </div>
    </div>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/utils.js"></script>
    <script src="assets/js/supabase.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Login form submission
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const loginError = document.getElementById('loginError');
                
                // Validate inputs
                if (!username || !password) {
                    loginError.textContent = 'Please enter both username and password';
                    loginError.style.display = 'block';
                    return;
                }
                
                // Attempt login
                const result = await appAuth.loginUser(username, password);
                
                if (result.success) {
                    // Redirect based on role
                    const user = result.user;
                    if (user.role === 'admin') {
                        window.location.href = 'admin/index.html';
                    } else if (user.role === 'manager') {
                        window.location.href = 'manager/team.html';
                    } else if (user.role === 'employee') {
                        window.location.href = 'employee/my-profile.html';
                    } else {
                        loginError.textContent = 'Unknown user role';
                        loginError.style.display = 'block';
                    }
                } else {
                    // Show error message
                    loginError.textContent = result.message;
                    loginError.style.display = 'block';
                }
            });
            
            // Check if already logged in
            if (appAuth.isAuthenticated()) {
                const user = appAuth.getCurrentUser();
                if (user.role === 'admin') {
                    window.location.href = 'admin/index.html';
                } else if (user.role === 'manager') {
                    window.location.href = 'manager/team.html';
                } else if (user.role === 'employee') {
                    window.location.href = 'employee/my-profile.html';
                }
            }
        });
    </script>
</body>
</html>