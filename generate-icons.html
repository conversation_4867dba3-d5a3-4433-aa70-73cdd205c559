<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator</title>
</head>
<body>
    <h1>Performance Management System - Icon Generator</h1>
    <p>This tool will generate all required icon files from your SVG.</p>
    
    <div id="canvases" style="display: none;"></div>
    
    <button onclick="generateIcons()">Generate All Icons</button>
    <div id="status"></div>
    <div id="downloads"></div>

    <script>
        const svgContent = `<svg xmlns="http://www.w3.org/2000/svg" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 512 493.46"><g fill-rule="nonzero"><path fill="#6BBE66" d="M103.74 355.45v47.06h59.3v-47.06h-59.3zm317.89-135.52v182.58h59.3V219.93h-59.3zm-105.95 51.91v130.67h59.26V271.84h-59.26zm-105.95 34.08v96.59h59.26v-96.59h-59.26z"/><path fill="#393939" d="M51.99 87.96v353.51h450.06v51.99H26.02C11.66 493.46 0 481.81 0 467.45V87.96h51.99zm417.83-14.19l4.31 33.78c1.33 10.41 10.83 17.76 21.25 16.47 10.41-1.32 17.76-10.83 16.47-21.24l-10.99-85.57c-.58-6.01-4.03-11.66-9.67-14.81-5.15-2.86-11-3.07-16.1-1.12l-82.41 31.74c-9.79 3.78-14.65 14.78-10.87 24.57 3.77 9.79 14.77 14.65 24.56 10.87l28.14-10.83c-30.84 41.83-67.77 76.77-107.64 103.99-57.43 39.21-120.76 62.24-180.55 66.56-10.5.74-18.38 9.83-17.64 20.33.75 10.5 9.84 18.38 20.34 17.63 66.35-4.68 136.31-30 199.3-73.03 45.31-30.91 87.1-71.08 121.5-119.34z"/></g></svg>`;

        const iconSizes = [
            { name: 'favicon-16x16.png', size: 16 },
            { name: 'favicon-32x32.png', size: 32 },
            { name: 'apple-touch-icon.png', size: 180 },
            { name: 'android-chrome-192x192.png', size: 192 },
            { name: 'android-chrome-512x512.png', size: 512 },
            { name: 'mstile-150x150.png', size: 150 },
            { name: 'favicon.ico', size: 32, isIco: true }
        ];

        function generateIcons() {
            const status = document.getElementById('status');
            const downloads = document.getElementById('downloads');
            const canvasContainer = document.getElementById('canvases');
            
            status.innerHTML = 'Generating icons...';
            downloads.innerHTML = '';
            canvasContainer.innerHTML = '';

            iconSizes.forEach((iconInfo, index) => {
                setTimeout(() => {
                    generateIcon(iconInfo.size, iconInfo.name, canvasContainer, downloads);
                    if (index === iconSizes.length - 1) {
                        status.innerHTML = 'All icons generated! Right-click each link and "Save As" to download.';
                    }
                }, index * 100);
            });
        }

        function generateIcon(size, filename, container, downloads) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // Create SVG blob and image
            const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(svgBlob);
            const img = new Image();

            img.onload = function() {
                // Fill background with white for better visibility
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, size, size);
                
                // Draw the SVG image
                ctx.drawImage(img, 0, 0, size, size);
                
                // Convert to blob and create download link
                const format = filename.endsWith('.ico') ? 'image/x-icon' : 'image/png';
                canvas.toBlob(function(blob) {
                    const downloadUrl = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = downloadUrl;
                    link.download = filename;
                    link.textContent = `Download ${filename} (${size}x${size})`;
                    if (filename.endsWith('.ico')) {
                        link.textContent += ' - For root directory';
                        link.style.fontWeight = 'bold';
                        link.style.color = '#0d6efd';
                    }
                    link.style.display = 'block';
                    link.style.margin = '5px 0';
                    downloads.appendChild(link);
                }, format);
                
                URL.revokeObjectURL(url);
            };

            img.src = url;
            container.appendChild(canvas);
        }
    </script>
</body>
</html>
