<!-- Settings Modal -->
<div class="settings-modal" id="settingsModal">
    <div class="settings-content">
        <div class="modal-header">
            <h5 class="modal-title">Settings</h5>
            <button class="close-btn" onclick="closeSettingsModal()">&times;</button>
        </div>
        <div class="modal-body">
            <h6 class="mb-3">Change Password</h6>
            <form id="changePasswordForm">
                <div class="form-group mb-3">
                    <label for="currentPassword" class="form-label">Current Password</label>
                    <div class="input-group">
                        <input type="password" id="currentPassword" class="form-control" required>
                        <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('currentPassword')">
                            <i class="fas fa-eye" id="currentPasswordIcon"></i>
                        </button>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label for="newPassword" class="form-label">New Password</label>
                    <div class="input-group">
                        <input type="password" id="newPassword" class="form-control" required>
                        <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('newPassword')">
                            <i class="fas fa-eye" id="newPasswordIcon"></i>
                        </button>
                    </div>
                    <small class="text-muted">4-12 characters, letters, numbers, and must contain _ or @</small>
                </div>
                <div class="form-group mb-3">
                    <label for="confirmPassword" class="form-label">Confirm New Password</label>
                    <div class="input-group">
                        <input type="password" id="confirmPassword" class="form-control" required>
                        <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('confirmPassword')">
                            <i class="fas fa-eye" id="confirmPasswordIcon"></i>
                        </button>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">Save Password</button>
                    <button type="button" class="btn btn-secondary" onclick="closeSettingsModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Notifications Modal -->
<div class="notifications-modal" id="notificationsModal">
    <div class="notifications-content">
        <div class="modal-header">
            <h5 class="modal-title">Notifications</h5>
            <button class="close-btn" onclick="closeNotificationsModal()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="notificationsList">
                <div class="empty-notifications">
                    <i class="fas fa-bell-slash fa-2x mb-2"></i>
                    <p>No notifications</p>
                </div>
            </div>
            <div class="notification-actions">
                <button class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">Mark All as Read</button>
                <button class="btn btn-outline-danger btn-sm" onclick="clearAllNotifications()">Clear All</button>
            </div>
        </div>
    </div>
</div>
