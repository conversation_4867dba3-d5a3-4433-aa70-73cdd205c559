<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign Appraisals - HR Performance Evaluation System</title>
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="icon" type="image/png" sizes="96x96" href="../assets/icons/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" sizes="any" href="../assets/icons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="../assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="../assets/icons/web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../assets/icons/web-app-manifest-512x512.png">
    <link rel="manifest" href="../manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="msapplication-config" content="../assets/icons/browserconfig.xml">
    
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Enhanced form styling */
        .form-select, .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-select:focus, .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Button improvements */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* Card enhancements */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Status badges */
        .badge {
            border-radius: 20px;
            font-weight: 500;
        }

        .badge-success {
            background: linear-gradient(45deg, #28a745, #1e7e34);
        }

        .badge-warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
        }

        .badge-danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }

        /* Table improvements */
        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Success animations */
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .success-animation {
            animation: successPulse 0.6s ease-in-out;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="kpis.html" class="nav-link">
                    <i class="fas fa-bullseye"></i> KPIs
                </a>
                <a href="assign-kpis.html" class="nav-link">
                    <i class="fas fa-user-tag"></i> Assign KPIs
                </a>
                <a href="assign-appraisals.html" class="nav-link active">
                    <i class="fas fa-clipboard-list"></i> Assign Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Assign Appraisals</h1>
                <div>
                    <button id="bulkAssignBtn" class="btn btn-primary">
                        <i class="fas fa-users mr-1"></i> Bulk Assign
                    </button>
                </div>
            </div>
            
            <!-- Assignment Form -->
            <div class="card mb-4">
                <div class="card-header py-2">
                    <h5 class="mb-0">New Assignment</h5>
                </div>
                <div class="card-body">
                    <form id="assignmentForm">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="yearSelect" class="form-label">Year *</label>
                                    <select id="yearSelect" class="form-select" required>
                                        <option value="">Select Year</option>
                                        <!-- Years will be loaded dynamically -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="periodTypeSelect" class="form-label">Period Type *</label>
                                    <select id="periodTypeSelect" class="form-select" required>
                                        <option value="">Select Period Type</option>
                                        <option value="Q1">Q1</option>
                                        <option value="Q2">Q2</option>
                                        <option value="Q3">Q3</option>
                                        <option value="Q4">Q4</option>
                                        <option value="Semester 1">Semester 1</option>
                                        <option value="Semester 2">Semester 2</option>
                                        <option value="Annual">Annual</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="departmentSelect" class="form-label">Department</label>
                                    <select id="departmentSelect" class="form-select">
                                        <option value="">All Departments</option>
                                        <!-- Departments will be loaded dynamically -->
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="managerSelect" class="form-label">Manager</label>
                                    <select id="managerSelect" class="form-select">
                                        <option value="">All Managers</option>
                                        <!-- Managers will be loaded dynamically -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notesInput" class="form-label">Notes</label>
                                    <textarea id="notesInput" class="form-control" rows="3" placeholder="Optional notes for this assignment..."></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button type="button" id="loadEmployeesBtn" class="btn btn-info">
                                <i class="fas fa-search mr-1"></i> Load Employees
                            </button>
                            <button type="submit" class="btn btn-success ml-2" disabled>
                                <i class="fas fa-check mr-1"></i> Assign Selected
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Employee Selection -->
            <div class="card mb-4" id="employeeSelectionCard" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center py-2">
                    <h5 class="mb-0">Select Employees</h5>
                    <div>
                        <button id="selectAllBtn" class="btn btn-light btn-sm">Select All</button>
                        <button id="deselectAllBtn" class="btn btn-light btn-sm ms-1">Deselect All</button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="employeeGrid" class="row">
                        <!-- Employee cards will be loaded dynamically -->
                    </div>
                </div>
            </div>
            
            <!-- Current Assignments -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center py-2">
                    <h5 class="mb-0">Current Assignments</h5>
                    <div>
                        <select id="statusFilter" class="form-select form-select-sm" style="width: auto; display: inline-block;">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                        </select>
                        <button id="refreshAssignmentsBtn" class="btn btn-outline-primary btn-sm ml-2">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="assignmentsTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Period</th>
                                    <th>Status</th>
                                    <th>Assigned Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="assignmentsTableBody">
                                <!-- Assignments will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('admin')) {
                return;
            }

            // Load current user name
            loadCurrentUserName();
            
            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Load initial data
            loadPeriods();
            loadDepartments();
            loadManagers();
            loadCurrentAssignments();
            
            // Event listeners
            document.getElementById('yearSelect').addEventListener('change', function() {
                // Clear employee list when year changes
                document.getElementById('employeesList').innerHTML = '';
                document.getElementById('selectedCount').textContent = '0';
            });
            document.getElementById('periodTypeSelect').addEventListener('change', function() {
                // Clear employee list when period type changes
                document.getElementById('employeesList').innerHTML = '';
                document.getElementById('selectedCount').textContent = '0';
            });
            document.getElementById('loadEmployeesBtn').addEventListener('click', loadEmployees);
            document.getElementById('assignmentForm').addEventListener('submit', handleAssignment);
            document.getElementById('selectAllBtn').addEventListener('click', selectAllEmployees);
            document.getElementById('deselectAllBtn').addEventListener('click', deselectAllEmployees);
            document.getElementById('refreshAssignmentsBtn').addEventListener('click', loadCurrentAssignments);
            document.getElementById('statusFilter').addEventListener('change', loadCurrentAssignments);
        });

        // Load years from periods
        async function loadPeriods() {
            try {
                const { data: periods, error } = await supabaseClient
                    .from('appraisal_periods')
                    .select('id, name, start_date, end_date')
                    .order('start_date', { ascending: false });

                if (error) throw error;

                // Extract unique years from period names
                const years = [...new Set(periods.map(period => {
                    const match = period.name.match(/\d{4}/);
                    return match ? parseInt(match[0]) : null;
                }).filter(year => year !== null))].sort((a, b) => b - a);

                const yearSelect = document.getElementById('yearSelect');
                const currentYear = new Date().getFullYear();

                years.forEach(year => {
                    const option = document.createElement('option');
                    option.value = year;
                    option.textContent = year;
                    yearSelect.appendChild(option);
                });

                // Auto-select current year if available
                if (years.includes(currentYear)) {
                    yearSelect.value = currentYear;
                }

                // Store periods data for later use
                window.allPeriods = periods;
            } catch (error) {
                console.error('Error loading periods:', error);
                appUtils.showNotification('Error loading periods', 'error');
            }
        }

        // Load departments
        async function loadDepartments() {
            try {
                const { data: departments, error } = await supabaseClient
                    .from('employees')
                    .select('department')
                    .order('department');

                if (error) throw error;

                const uniqueDepartments = [...new Set(departments.map(item => item.department))];
                const departmentSelect = document.getElementById('departmentSelect');

                uniqueDepartments.forEach(department => {
                    const option = document.createElement('option');
                    option.value = department;
                    option.textContent = department;
                    departmentSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading departments:', error);
            }
        }

        // Load managers
        async function loadManagers() {
            try {
                const { data: managers, error } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .eq('is_manager', true)
                    .order('name');

                if (error) throw error;

                const managerSelect = document.getElementById('managerSelect');
                managers.forEach(manager => {
                    const option = document.createElement('option');
                    option.value = manager.code_number;
                    option.textContent = manager.name;
                    managerSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading managers:', error);
            }
        }

        // Load employees based on filters
        async function loadEmployees() {
            try {
                const year = document.getElementById('yearSelect').value;
                const periodType = document.getElementById('periodTypeSelect').value;

                if (!year || !periodType) {
                    appUtils.showNotification('Please select both year and period type first', 'warning');
                    return;
                }

                // Find the matching period ID
                let periodId = null;
                if (window.allPeriods) {
                    const matchingPeriod = window.allPeriods.find(period => {
                        const yearMatch = period.name.match(/\d{4}/);
                        return yearMatch && yearMatch[0] === year && period.name.startsWith(periodType + ' ');
                    });

                    if (matchingPeriod) {
                        periodId = matchingPeriod.id;
                    } else {
                        appUtils.showNotification('No matching period found for the selected year and period type', 'warning');
                        return;
                    }
                }

                const department = document.getElementById('departmentSelect').value;
                const manager = document.getElementById('managerSelect').value;

                // Build query - include ALL employees (managers and non-managers)
                let query = supabaseClient
                    .from('employees')
                    .select('*');

                if (department) {
                    query = query.eq('department', department);
                }

                if (manager) {
                    query = query.eq('manager_code', manager);
                }

                const { data: employees, error } = await query.order('name');

                if (error) throw error;

                // Get existing assignments for this period
                const { data: existingAssignments } = await supabaseClient
                    .from('appraisal_assignments')
                    .select('employee_code, status')
                    .eq('period_id', periodId);

                const assignedEmployees = new Set(existingAssignments?.map(a => a.employee_code) || []);

                // Display employees
                displayEmployees(employees || [], assignedEmployees);

                // Show employee selection card
                document.getElementById('employeeSelectionCard').style.display = 'block';

                // Enable assign button
                document.querySelector('button[type="submit"]').disabled = false;

            } catch (error) {
                console.error('Error loading employees:', error);
                appUtils.showNotification('Error loading employees', 'error');
            }
        }

        // Display employees in grid
        function displayEmployees(employees, assignedEmployees) {
            const grid = document.getElementById('employeeGrid');
            grid.innerHTML = '';

            if (employees.length === 0) {
                grid.innerHTML = '<div class="col-12"><p class="text-center">No employees found matching the criteria</p></div>';
                return;
            }

            employees.forEach(employee => {
                const isAssigned = assignedEmployees.has(employee.code_number);
                const cardClass = isAssigned ? 'border-warning' : 'border-light';
                const statusBadge = isAssigned ? '<span class="badge badge-warning">Already Assigned</span>' : '';

                const card = document.createElement('div');
                card.className = 'col-md-4 col-lg-3 mb-3';
                card.innerHTML = `
                    <div class="card ${cardClass}">
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input employee-checkbox" type="checkbox"
                                       value="${employee.code_number}" id="emp_${employee.code_number}"
                                       ${isAssigned ? 'disabled' : ''}>
                                <label class="form-check-label" for="emp_${employee.code_number}">
                                    <strong>${employee.name}</strong><br>
                                    <small class="text-muted">${employee.position}</small><br>
                                    <small class="text-muted">${employee.department}</small>
                                    ${statusBadge}
                                </label>
                            </div>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
        }

        // Select all employees
        function selectAllEmployees() {
            const checkboxes = document.querySelectorAll('.employee-checkbox:not(:disabled)');
            checkboxes.forEach(checkbox => checkbox.checked = true);
        }

        // Deselect all employees
        function deselectAllEmployees() {
            const checkboxes = document.querySelectorAll('.employee-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = false);
        }

        // Handle assignment form submission
        async function handleAssignment(e) {
            e.preventDefault();

            try {
                const year = document.getElementById('yearSelect').value;
                const periodType = document.getElementById('periodTypeSelect').value;
                const notes = document.getElementById('notesInput').value;
                const currentUser = appAuth.getCurrentEmployee();

                if (!year || !periodType) {
                    appUtils.showNotification('Please select both year and period type', 'warning');
                    return;
                }

                // Find the matching period ID
                let periodId = null;
                if (window.allPeriods) {
                    const matchingPeriod = window.allPeriods.find(period => {
                        const yearMatch = period.name.match(/\d{4}/);
                        return yearMatch && yearMatch[0] === year && period.name.startsWith(periodType + ' ');
                    });

                    if (matchingPeriod) {
                        periodId = matchingPeriod.id;
                    } else {
                        appUtils.showNotification('No matching period found for the selected year and period type', 'warning');
                        return;
                    }
                }

                // Get selected employees
                const selectedEmployees = [];
                const checkboxes = document.querySelectorAll('.employee-checkbox:checked');
                checkboxes.forEach(checkbox => {
                    selectedEmployees.push(checkbox.value);
                });

                if (selectedEmployees.length === 0) {
                    appUtils.showNotification('Please select at least one employee', 'warning');
                    return;
                }

                // Create assignments
                const assignments = selectedEmployees.map(employeeCode => ({
                    employee_code: employeeCode,
                    period_id: periodId,
                    assigned_by: currentUser.code_number,
                    status: 'pending',
                    notes: notes || null
                }));

                const { error } = await supabaseClient
                    .from('appraisal_assignments')
                    .insert(assignments);

                if (error) throw error;

                // Add success animation to submit button
                const submitBtn = document.querySelector('button[type="submit"]');
                submitBtn.classList.add('success-animation');
                setTimeout(() => {
                    submitBtn.classList.remove('success-animation');
                }, 600);

                appUtils.showNotification(`Successfully assigned appraisals to ${selectedEmployees.length} employees`, 'success');

                // Reset form and reload data
                document.getElementById('assignmentForm').reset();
                document.getElementById('employeeSelectionCard').style.display = 'none';
                submitBtn.disabled = true;
                loadCurrentAssignments();

            } catch (error) {
                console.error('Error creating assignments:', error);
                appUtils.showNotification('Error creating assignments', 'error');
            }
        }

        // Load current assignments
        async function loadCurrentAssignments() {
            try {
                const statusFilter = document.getElementById('statusFilter').value;

                let query = supabaseClient
                    .from('appraisal_assignments')
                    .select(`
                        *,
                        employee:employees!appraisal_assignments_employee_code_fkey(code_number, name, position, department),
                        period:appraisal_periods(name),
                        assigned_by_employee:employees!appraisal_assignments_assigned_by_fkey(name)
                    `);

                if (statusFilter) {
                    query = query.eq('status', statusFilter);
                }

                const { data: assignments, error } = await query.order('assigned_at', { ascending: false });

                if (error) throw error;

                populateAssignmentsTable(assignments || []);

            } catch (error) {
                console.error('Error loading assignments:', error);
                appUtils.showNotification('Error loading assignments', 'error');
            }
        }

        // Populate assignments table
        function populateAssignmentsTable(assignments) {
            const tableBody = document.getElementById('assignmentsTableBody');
            tableBody.innerHTML = '';

            if (assignments.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 7;
                cell.textContent = 'No assignments found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }

            assignments.forEach(assignment => {
                const row = document.createElement('tr');

                // Employee
                const employeeCell = document.createElement('td');
                employeeCell.textContent = assignment.employee ? assignment.employee.name : 'N/A';
                row.appendChild(employeeCell);

                // Position
                const positionCell = document.createElement('td');
                positionCell.textContent = assignment.employee ? assignment.employee.position : 'N/A';
                row.appendChild(positionCell);

                // Department
                const departmentCell = document.createElement('td');
                departmentCell.textContent = assignment.employee ? assignment.employee.department : 'N/A';
                row.appendChild(departmentCell);

                // Period
                const periodCell = document.createElement('td');
                periodCell.textContent = assignment.period ? assignment.period.name : 'N/A';
                row.appendChild(periodCell);

                // Status
                const statusCell = document.createElement('td');
                let statusClass = 'badge-secondary';
                if (assignment.status === 'pending') statusClass = 'badge-danger';
                else if (assignment.status === 'in_progress') statusClass = 'badge-warning';
                else if (assignment.status === 'completed') statusClass = 'badge-success';

                statusCell.innerHTML = `<span class="badge ${statusClass}">${assignment.status.replace('_', ' ').toUpperCase()}</span>`;
                row.appendChild(statusCell);

                // Assigned Date
                const dateCell = document.createElement('td');
                dateCell.textContent = appUtils.formatReadableDate(assignment.assigned_at);
                row.appendChild(dateCell);

                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <button class="btn btn-danger btn-sm" onclick="removeAssignment('${assignment.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                row.appendChild(actionsCell);

                tableBody.appendChild(row);
            });
        }

        // Remove assignment
        async function removeAssignment(assignmentId) {
            if (!confirm('Are you sure you want to remove this assignment?')) {
                return;
            }

            try {
                const { error } = await supabaseClient
                    .from('appraisal_assignments')
                    .delete()
                    .eq('id', assignmentId);

                if (error) throw error;

                appUtils.showNotification('Assignment removed successfully', 'success');
                loadCurrentAssignments();

            } catch (error) {
                console.error('Error removing assignment:', error);
                appUtils.showNotification('Error removing assignment', 'error');
            }
        }

        // Make removeAssignment globally available
        window.removeAssignment = removeAssignment;

        // Load current user name and show welcome message
        async function loadCurrentUserName() {
            try {
                const currentUser = appAuth.getCurrentUser();
                if (currentUser) {
                    // Update display name to show only first name or "Master Admin"
                    appUtils.updateUserDisplayName('currentUserName', {
                        role: 'admin',
                        name: currentUser.username || currentUser.name
                    });

                    // Show welcome message
                    appUtils.showWelcomeMessage({
                        role: 'admin',
                        name: currentUser.username || currentUser.name
                    });
                } else {
                    document.getElementById('currentUserName').textContent = 'Admin';
                }
            } catch (error) {
                console.error('Error loading user name:', error);
                document.getElementById('currentUserName').textContent = 'Admin';
            }
        }
    </script>
</body>
</html>
