<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Appraisal - Performance Evaluation System</title>
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="icon" type="image/png" sizes="96x96" href="../assets/icons/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" sizes="any" href="../assets/icons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="../assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="../assets/icons/web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../assets/icons/web-app-manifest-512x512.png">
    <link rel="manifest" href="../manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="msapplication-config" content="../assets/icons/browserconfig.xml">
    

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- jsPDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern card styling */
        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Compact chart containers */
        .chart-container-compact {
            position: relative;
            height: 280px;
            margin: 10px 0;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container header-container">
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                Performance Evaluation System
            </div>
            <nav class="nav">
                <a href="my-profile.html" class="nav-link">My Profile</a>
                <a href="my-appraisal.html" class="nav-link active">My Appraisal</a>
            </nav>
        </div>
    </header>


    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Appraisal Report</h1>
                <div>
                    <a href="my-appraisal.html" class="btn btn-secondary mr-2">
                        <i class="fas fa-arrow-left mr-1"></i> Back to My Appraisal
                    </a>
                    <button id="downloadPdfBtn" class="btn btn-primary">
                        <i class="fas fa-download mr-1"></i> Download PDF
                    </button>
                </div>
            </div>

            <div class="appraisal-report">
                <div class="report-header">
                    <h2 class="report-title">Performance Appraisal Report</h2>
                    <p class="report-subtitle" id="reportPeriod"></p>

                    <div class="report-info">
                        <div class="info-item">
                            <div class="info-label">Employee</div>
                            <div id="employeeName"></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Position</div>
                            <div id="employeePosition"></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Department</div>
                            <div id="employeeDepartment"></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Manager</div>
                            <div id="managerName"></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Date</div>
                            <div id="appraisalDate"></div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Total Score</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="totalScoreGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Performance</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="performanceGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header py-2">
                                <h5 class="mb-0">Behavioral</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container-compact">
                                    <canvas id="behavioralGauge"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="score-summary">
                    <div>
                        <span class="summary-label">Total Score:</span>
                        <span id="totalScore" class="summary-value"></span>
                    </div>
                    <div>
                        <span class="summary-label">Grade:</span>
                        <span id="gradeValue" class="summary-value"></span>
                    </div>
                </div>

                <!-- Performance KPIs Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Performance KPIs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                    <th>Comments</th>
                                </tr>
                            </thead>
                            <tbody id="performanceKpisTable">
                                <!-- Performance KPIs will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Performance Total</th>
                                    <th id="performanceTotal"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Behavioral KPIs Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Behavioral KPIs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                    <th>Comments</th>
                                </tr>
                            </thead>
                            <tbody id="behavioralKpisTable">
                                <!-- Behavioral KPIs will be loaded dynamically -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Behavioral Total</th>
                                    <th id="behavioralTotal"></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Category Weights Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Category Weights</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Weight</th>
                                    <th>Score</th>
                                    <th>Weighted Score</th>
                                </tr>
                            </thead>
                            <tbody id="categoryWeightsTable">
                                <!-- Category weights will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Comments</h3>
                    <div class="comments-content" id="commentsContent">
                        <!-- Comments will be loaded dynamically -->
                    </div>
                </div>

                <!-- Signatures Section -->
                <div class="report-section">
                    <h3 class="report-section-title">Signatures</h3>
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Manager Signature</label>
                                <div id="managerSignature" class="signature-box">
                                    <!-- Manager signature status will be loaded dynamically -->
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Employee Signature</label>
                                <div id="employeeSignature" class="signature-box">
                                    <!-- Employee signature status will be loaded dynamically -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>


    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>

    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>

    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    <script src="../assets/js/charts.js"></script>
    <script src="../assets/js/pdf.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role - EMPLOYEE VERSION
            if (!appAuth.checkAuth('employee')) {
                return;
            }

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();

            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });

            // Get URL parameters
            const params = appUtils.getUrlParams();
            const appraisalId = params.id;

            if (!appraisalId) {
                appUtils.showNotification('Missing appraisal ID', 'error');
                setTimeout(() => {
                    window.location.href = 'my-appraisal.html';
                }, 2000);
                return;
            }

            // Load appraisal data
            loadAppraisalData(appraisalId);

            // Download PDF button
            document.getElementById('downloadPdfBtn').addEventListener('click', function() {
                downloadAppraisalPdf(appraisalId);
            });
        });


        // Global chart instances for cleanup
        let totalScoreChart = null;
        let performanceChart = null;
        let behavioralChart = null;

        // Load appraisal data - EMPLOYEE VERSION
        async function loadAppraisalData(appraisalId) {
            try {
                // Destroy existing charts before creating new ones
                if (totalScoreChart) {
                    totalScoreChart.destroy();
                    totalScoreChart = null;
                }
                if (performanceChart) {
                    performanceChart.destroy();
                    performanceChart = null;
                }
                if (behavioralChart) {
                    behavioralChart.destroy();
                    behavioralChart = null;
                }
                console.log('View appraisal: Loading appraisal data for ID:', appraisalId);
                // Get current employee
                const currentEmployee = appAuth.getCurrentEmployee();
                console.log('View appraisal: Current employee:', currentEmployee);
                if (!currentEmployee) {
                    appUtils.showNotification('Employee information not found', 'error');
                    return;
                }

                // Get appraisal data - EMPLOYEE SECURITY: Only own appraisals
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*),
                        period:appraisal_periods(*)
                    `)
                    .eq('id', appraisalId)
                    .eq('employee_code', currentEmployee.code_number) // SECURITY: Employee can only view own
                    .single();

                if (appraisalError) throw appraisalError;

                if (!appraisal) {
                    appUtils.showNotification('You are not authorized to view this appraisal', 'error');
                    setTimeout(() => {
                        window.location.href = 'my-appraisal.html';
                    }, 2000);
                    return;
                }

                // Display appraisal information
                document.getElementById('reportPeriod').textContent = appraisal.period ? appraisal.period.name : 'N/A';
                document.getElementById('employeeName').textContent = appraisal.employee ? appraisal.employee.name : 'N/A';
                document.getElementById('employeePosition').textContent = appraisal.employee ? appraisal.employee.position : 'N/A';
                document.getElementById('employeeDepartment').textContent = appraisal.employee ? appraisal.employee.department : 'N/A';
                document.getElementById('managerName').textContent = appraisal.manager ? appraisal.manager.name : 'N/A';
                document.getElementById('appraisalDate').textContent = appUtils.formatReadableDate(appraisal.created_at);

                // Display scores and grade
                document.getElementById('totalScore').textContent = `${appraisal.total_score.toFixed(1)}%`;
                document.getElementById('gradeValue').textContent = appraisal.grade;
                document.getElementById('gradeValue').className = `summary-value grade-${appraisal.grade.toLowerCase().replace(' ', '-')}`;

                // Create gauge charts and store instances for cleanup
                totalScoreChart = appCharts.createGaugeChart('totalScoreGauge', appraisal.total_score, 'Overall Performance');
                performanceChart = appCharts.createGaugeChart('performanceGauge', appraisal.performance_score, 'Performance');
                behavioralChart = appCharts.createGaugeChart('behavioralGauge', appraisal.behavioral_score, 'Behavioral');

                // Get appraisal scores - Using historical data for integrity
                const { data: appraisalScores, error: scoresError } = await supabaseClient
                    .from('appraisal_scores')
                    .select('*')
                    .eq('appraisal_id', appraisalId);

                if (scoresError) throw scoresError;

                // Get original KPI assignment order to match create-appraisal order
                const kpiIds = appraisalScores.map(score => score.employee_kpi_id).filter(id => id);
                if (kpiIds.length > 0) {
                    const { data: kpiOrder } = await supabaseClient
                        .from('employee_kpis')
                        .select('id, created_at')
                        .in('id', kpiIds)
                        .order('created_at');

                    // Create order map
                    const orderMap = {};
                    (kpiOrder || []).forEach((kpi, index) => {
                        orderMap[kpi.id] = index;
                    });

                    // Sort scores by original assignment order
                    appraisalScores.sort((a, b) => {
                        const orderA = orderMap[a.employee_kpi_id] ?? 999;
                        const orderB = orderMap[b.employee_kpi_id] ?? 999;
                        return orderA - orderB;
                    });
                }

                // Separate scores by category
                const performanceScores = [];
                const behavioralScores = [];

                appraisalScores.forEach(score => {
                    // Use historical data from appraisal_scores table (preserves data integrity)
                    const category = score.kpi_category_name;

                    if (category === 'Performance') {
                        performanceScores.push({
                            id: score.id,
                            name: score.kpi_name,
                            weight: score.kpi_weight,
                            score: score.score,
                            comments: score.comments
                        });
                    } else if (category === 'Behavioral') {
                        behavioralScores.push({
                            id: score.id,
                            name: score.kpi_name,
                            weight: score.kpi_weight,
                            score: score.score,
                            comments: score.comments
                        });
                    }
                });

                // Display KPI scores
                displayKpiScores('performanceKpisTable', performanceScores);
                displayKpiScores('behavioralKpisTable', behavioralScores);

                // Display category weights and scores
                document.getElementById('performanceTotal').textContent = `${appraisal.performance_score.toFixed(1)}%`;
                document.getElementById('behavioralTotal').textContent = `${appraisal.behavioral_score.toFixed(1)}%`;

                // Get category weights
                const { data: categoryWeights } = await supabaseClient
                    .from('category_weights')
                    .select(`
                        *,
                        category:kpi_categories(*)
                    `)
                    .eq('employee_code', appraisal.employee_code);

                // Display category weights
                displayCategoryWeights('categoryWeightsTable', categoryWeights, appraisal);

                // Display comments
                document.getElementById('commentsContent').textContent = appraisal.comments || 'No comments provided.';

                // Update signatures (employee version - read-only)
                updateSignatures(appraisal);

            } catch (error) {
                console.error('Error loading appraisal data:', error);
                appUtils.showNotification('Error loading appraisal data', 'error');
            }
        }


        // Display KPI scores
        function displayKpiScores(tableId, scores) {
            const tableBody = document.getElementById(tableId);
            tableBody.innerHTML = '';

            if (scores.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 5;
                cell.textContent = 'No KPIs found for this category';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }

            scores.forEach(score => {
                const row = document.createElement('tr');

                // KPI Name
                const nameCell = document.createElement('td');
                nameCell.textContent = score.name;
                row.appendChild(nameCell);

                // Weight
                const weightCell = document.createElement('td');
                weightCell.textContent = `${score.weight}%`;
                row.appendChild(weightCell);

                // Score
                const scoreCell = document.createElement('td');
                scoreCell.textContent = `${score.score}/5`;
                row.appendChild(scoreCell);

                // Weighted Score
                const weightedScore = appUtils.calculateWeightedScore(score.score, score.weight);
                const weightedScoreCell = document.createElement('td');
                weightedScoreCell.textContent = `${weightedScore.toFixed(1)}`;
                row.appendChild(weightedScoreCell);

                // Comments
                const commentsCell = document.createElement('td');
                commentsCell.textContent = score.comments || 'No comments';
                row.appendChild(commentsCell);

                tableBody.appendChild(row);
            });
        }

        // Display category weights
        function displayCategoryWeights(tableId, categoryWeights, appraisal) {
            const tableBody = document.getElementById(tableId);
            tableBody.innerHTML = '';

            if (!categoryWeights || categoryWeights.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 4;
                cell.textContent = 'No category weights found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }

            categoryWeights.forEach(categoryWeight => {
                if (categoryWeight.category) {
                    const row = document.createElement('tr');

                    // Category Name
                    const nameCell = document.createElement('td');
                    nameCell.textContent = categoryWeight.category.name;
                    row.appendChild(nameCell);

                    // Weight
                    const weightCell = document.createElement('td');
                    weightCell.textContent = `${categoryWeight.weight}%`;
                    row.appendChild(weightCell);

                    // Score
                    const scoreCell = document.createElement('td');
                    const score = categoryWeight.category.name === 'Performance' ?
                        appraisal.performance_score :
                        appraisal.behavioral_score;

                    scoreCell.textContent = `${score.toFixed(1)}%`;
                    row.appendChild(scoreCell);

                    // Weighted Score
                    const weightedScore = (score * (categoryWeight.weight / 100));
                    const weightedScoreCell = document.createElement('td');
                    weightedScoreCell.textContent = `${weightedScore.toFixed(1)}%`;
                    row.appendChild(weightedScoreCell);

                    tableBody.appendChild(row);
                }
            });
        }

        // Update signatures - EMPLOYEE VERSION (read-only)
        function updateSignatures(appraisal) {
            const managerSignatureBox = document.getElementById('managerSignature');
            const employeeSignatureBox = document.getElementById('employeeSignature');

            // Manager signature (read-only for employee)
            if (appraisal.manager_signature) {
                managerSignatureBox.innerHTML = `
                    <div class="signature-signed">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Signed by ${appraisal.manager ? appraisal.manager.name : 'Manager'}</span>
                    </div>
                `;
            } else {
                managerSignatureBox.innerHTML = `
                    <div class="signature-pending">
                        <i class="fas fa-clock text-warning"></i>
                        <span>Pending signature</span>
                    </div>
                `;
            }

            // Employee signature - NEW WORKFLOW
            if (appraisal.employee_signature) {
                employeeSignatureBox.innerHTML = `
                    <div class="signature-signed">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Signed by ${appraisal.employee ? appraisal.employee.name : 'Employee'}</span>
                    </div>
                `;
            } else if (appraisal.employee_signature_requested) {
                // Show sign button when signature is requested
                employeeSignatureBox.innerHTML = `
                    <button id="signAsEmployee" class="btn btn-success btn-sm">
                        <i class="fas fa-signature mr-1"></i> Sign as Employee
                    </button>
                `;

                // Add event listener
                document.getElementById('signAsEmployee').addEventListener('click', function() {
                    signAsEmployee(appraisal.id);
                });
            } else {
                employeeSignatureBox.innerHTML = `
                    <div class="signature-pending">
                        <i class="fas fa-clock text-warning"></i>
                        <span>Awaiting manager request</span>
                    </div>
                `;
            }
        }

        // Sign as employee - NEW FUNCTION
        async function signAsEmployee(appraisalId) {
            try {
                // Get current employee
                const currentEmployee = appAuth.getCurrentEmployee();
                if (!currentEmployee) {
                    appUtils.showNotification('Employee information not found', 'error');
                    return;
                }

                // Verify this is the employee's own appraisal
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select('employee_code, employee_signature_requested, manager_signature')
                    .eq('id', appraisalId)
                    .single();

                if (appraisalError) throw appraisalError;

                if (appraisal.employee_code !== currentEmployee.code_number) {
                    appUtils.showNotification('You can only sign your own appraisal', 'error');
                    return;
                }

                if (!appraisal.employee_signature_requested) {
                    appUtils.showNotification('Signature has not been requested yet', 'error');
                    return;
                }

                if (!appraisal.manager_signature) {
                    appUtils.showNotification('Manager must sign first', 'error');
                    return;
                }

                // Update employee signature
                const { error: updateError } = await supabaseClient
                    .from('appraisals')
                    .update({
                        employee_signature: currentEmployee.name,
                        employee_signature_date: new Date().toISOString()
                    })
                    .eq('id', appraisalId);

                if (updateError) throw updateError;

                // Show success message
                appUtils.showNotification('Signed successfully', 'success');

                // Reload appraisal data
                loadAppraisalData(appraisalId);

            } catch (error) {
                console.error('Error signing appraisal:', error);
                appUtils.showNotification('Error signing appraisal', 'error');
            }
        }

        // Download PDF function - EMPLOYEE VERSION
        async function downloadAppraisalPdf(appraisalId) {
            try {
                console.log('Downloading PDF for appraisal:', appraisalId);

                // Get current employee
                const currentEmployee = appAuth.getCurrentEmployee();
                if (!currentEmployee) {
                    appUtils.showNotification('Employee information not found', 'error');
                    return;
                }

                // Get appraisal data for PDF
                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .select(`
                        *,
                        employee:employees!appraisals_employee_code_fkey(*),
                        manager:employees!appraisals_manager_code_fkey(*),
                        period:appraisal_periods(*)
                    `)
                    .eq('id', appraisalId)
                    .eq('employee_code', currentEmployee.code_number) // SECURITY: Employee can only download own
                    .single();

                if (appraisalError) throw appraisalError;

                // Get appraisal scores - Using historical data for integrity
                const { data: appraisalScores, error: scoresError } = await supabaseClient
                    .from('appraisal_scores')
                    .select('*')
                    .eq('appraisal_id', appraisalId);

                if (scoresError) throw scoresError;

                // Get original KPI assignment order to match create-appraisal order
                const kpiIds = appraisalScores.map(score => score.employee_kpi_id).filter(id => id);
                if (kpiIds.length > 0) {
                    const { data: kpiOrder } = await supabaseClient
                        .from('employee_kpis')
                        .select('id, created_at')
                        .in('id', kpiIds)
                        .order('created_at');

                    // Create order map
                    const orderMap = {};
                    (kpiOrder || []).forEach((kpi, index) => {
                        orderMap[kpi.id] = index;
                    });

                    // Sort scores by original assignment order
                    appraisalScores.sort((a, b) => {
                        const orderA = orderMap[a.employee_kpi_id] ?? 999;
                        const orderB = orderMap[b.employee_kpi_id] ?? 999;
                        return orderA - orderB;
                    });
                }

                // Get category weights
                const { data: categoryWeights } = await supabaseClient
                    .from('category_weights')
                    .select(`
                        *,
                        category:kpi_categories(*)
                    `)
                    .eq('employee_code', appraisal.employee_code);

                // Prepare data for PDF - Use historical data for integrity
                const scores = appraisalScores.map(score => ({
                    name: score.kpi_name,
                    weight: score.kpi_weight,
                    score: score.score,
                    comments: score.comments,
                    category: score.kpi_category_name
                }));

                const pdfData = {
                    employee: appraisal.employee,
                    manager: appraisal.manager,
                    period: appraisal.period,
                    created_at: appraisal.created_at,
                    total_score: appraisal.total_score,
                    performance_score: appraisal.performance_score,
                    behavioral_score: appraisal.behavioral_score,
                    grade: appraisal.grade,
                    comments: appraisal.comments,
                    scores: scores,
                    category_weights: categoryWeights.map(cw => ({
                        category: cw.category.name,
                        weight: cw.weight
                    })),
                    manager_signature: appraisal.manager_signature,
                    employee_signature: appraisal.employee_signature
                };

                // Generate and download PDF
                const fileName = `Appraisal_${appraisal.employee.name.replace(/\s+/g, '_')}_${appraisal.period.name.replace(/\s+/g, '_')}.pdf`;
                const success = await appPDF.downloadAppraisalPDF(pdfData, fileName);

                if (!success) {
                    appUtils.showNotification('Error generating PDF', 'error');
                }

            } catch (error) {
                console.error('Error downloading PDF:', error);
                appUtils.showNotification('Error downloading PDF', 'error');
            }
        }
    </script>

    <style>
        .signature-box {
            border: 1px dashed var(--secondary-color);
            padding: 1rem;
            text-align: center;
            margin-top: 0.5rem;
            border-radius: var(--border-radius);
        }

        .signature-signed {
            color: var(--success-color);
            font-weight: bold;
        }

        .signature-pending {
            color: var(--warning-color);
            font-style: italic;
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });
    </style>
</body>
</html>
