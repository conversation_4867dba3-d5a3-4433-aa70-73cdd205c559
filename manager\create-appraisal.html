<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Appraisal - Performance Evaluation System</title>
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="icon" type="image/png" sizes="96x96" href="../assets/icons/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" sizes="any" href="../assets/icons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="../assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="../assets/icons/web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../assets/icons/web-app-manifest-512x512.png">
    <link rel="manifest" href="../manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="msapplication-config" content="../assets/icons/browserconfig.xml">
    
    <!-- CALCULATION FIXED: v3.0 - ACTUAL Category Percentages Display Fixed -->
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Enhanced form styling */
        .form-select, .form-control, .form-control:focus {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-select:focus, .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Button improvements */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* Card enhancements */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .card-header {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-bottom: 1px solid #e0e0e0;
            border-radius: 8px 8px 0 0 !important;
        }

        /* KPI scoring section */
        .kpi-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        /* Score buttons styling */
        .score-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: flex-start;
            margin-bottom: 15px;
        }

        .score-btn {
            border: 2px solid #dee2e6;
            background: white;
            color: #495057;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-align: center;
            width: 120px;
            height: 70px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .score-btn:hover {
            border-color: #007bff;
            background: #f8f9fa;
            transform: translateY(-2px);
        }

        .score-btn.selected {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }

        .score-btn .score-number {
            display: block;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .score-btn .score-label {
            display: block;
            font-size: 0.75em;
            padding: 3px 8px;
            border-radius: 12px;
            background: #f8f9fa;
            color: #495057;
            transition: all 0.3s ease;
        }

        /* Score button label colors when selected */
        .score-btn[data-score="1"].selected .score-label {
            background: #dc3545;
            color: white;
        }

        .score-btn[data-score="2"].selected .score-label {
            background: #fd7e14;
            color: white;
        }

        .score-btn[data-score="3"].selected .score-label {
            background: #198754;
            color: white;
        }

        .score-btn[data-score="4"].selected .score-label {
            background: #0d6efd;
            color: white;
        }

        .score-btn[data-score="5"].selected .score-label {
            background: #ffc107;
            color: #000;
        }

        /* KPI Comments styling */
        .kpi-comments {
            margin-top: 10px;
        }

        .kpi-comments textarea {
            height: 60px;
            resize: vertical;
        }

        /* Category headers */
        .category-header {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        /* Total score summary */
        .total-score-summary {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: sticky;
            top: 80px;
            z-index: 100;
        }

        .score-summary-item {
            text-align: center;
            padding: 10px;
        }

        .score-summary-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #1976d2;
        }

        .score-summary-label {
            font-size: 0.9em;
            color: #666;
        }

        /* Navigation styling */
        .nav-bottom {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 8px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .nav-bottom .nav-link {
            color: white !important;
            font-weight: 500;
            padding: 8px 15px;
            margin: 0 2px;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .nav-bottom .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .nav-bottom .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        /* Header styling - matching other manager pages */
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 15px 0;
            margin-bottom: 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
        }

        .logo-icon {
            color: white;
            font-size: 1.5rem;
            margin-right: 10px;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-name {
            color: white;
            font-size: 0.9rem;
        }

        .logout-btn {
            color: white;
            text-decoration: none;
            font-size: 0.8rem;
            padding: 4px 8px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        .logout-btn:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }

        /* Navigation styling */
        .nav-bottom {
            background: rgba(255,255,255,0.1);
            padding: 10px 0;
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="team.html" class="nav-link">
                    <i class="fas fa-users"></i> My Team
                </a>
                <a href="appraisals.html" class="nav-link">
                    <i class="fas fa-clipboard-check"></i> Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
                <a href="my-appraisal.html" class="nav-link">
                    <i class="fas fa-user-check"></i> My Appraisal
                </a>
            </nav>
        </div>
    </header>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>Create Performance Appraisal
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Employee and Period Info -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Employee Information</h6>
                                        <div id="employeeInfo">
                                            <p><strong>Name:</strong> <span id="employeeName">Loading...</span></p>
                                            <p><strong>Position:</strong> <span id="employeePosition">Loading...</span></p>
                                            <p><strong>Department:</strong> <span id="employeeDepartment">Loading...</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Appraisal Period</h6>
                                        <div id="periodInfo">
                                            <p><strong>Period:</strong> <span id="periodName">Loading...</span></p>
                                            <p><strong>Duration:</strong> <span id="periodDuration">Loading...</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- KPI Scoring Section -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">KPI Scoring</h6>
                            </div>
                            <div class="card-body">
                                <div id="kpiScoringSection">
                                    <!-- KPIs will be loaded dynamically -->
                                </div>
                            </div>
                        </div>

                        <!-- Total Score Summary -->
                        <div class="total-score-summary" id="totalScoreSummary" style="display: none;">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-chart-pie me-2"></i>Performance Summary
                            </h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="score-summary-item">
                                        <div class="score-summary-value" id="performanceScore">0%</div>
                                        <div class="score-summary-label">Performance Score</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="score-summary-item">
                                        <div class="score-summary-value" id="behavioralScore">0%</div>
                                        <div class="score-summary-label">Behavioral Score</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="score-summary-item">
                                        <div class="score-summary-value" id="totalScore">0%</div>
                                        <div class="score-summary-label">Total Score</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="score-summary-item">
                                        <div class="score-summary-value" id="finalGrade">-</div>
                                        <div class="score-summary-label">Final Grade</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Comments Section -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Comments</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="managerComments" class="form-label">Manager Comments</label>
                                    <textarea class="form-control" id="managerComments" rows="4" 
                                              placeholder="Enter your comments about the employee's performance..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-secondary me-2" onclick="window.history.back()">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </button>
                            <button type="button" class="btn btn-primary" id="saveAppraisalBtn">
                                <i class="fas fa-save"></i> Save Appraisal
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>

    <script>
        let currentEmployee = null;
        let currentPeriod = null;
        let assignedKPIs = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('manager')) {
                return;
            }

            // Setup logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });

            // Load current user name
            loadCurrentUserName();

            // Get URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const employeeCode = urlParams.get('employee');
            const periodId = urlParams.get('period');

            if (!employeeCode || !periodId) {
                showNotification('Missing employee or period information', 'error');
                window.history.back();
                return;
            }

            // Setup navigation button effects
            setupNavigationEffects();

            // Load data
            loadAppraisalData(employeeCode, periodId);

            // Setup save button
            document.getElementById('saveAppraisalBtn').addEventListener('click', saveAppraisal);
        });

        // Setup navigation button effects
        function setupNavigationEffects() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all links
                    navLinks.forEach(l => l.classList.remove('pressed'));
                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        }

        // Load current user name and show welcome message
        async function loadCurrentUserName() {
            try {
                const currentManager = appAuth.getCurrentEmployee();
                if (currentManager) {
                    // Update user name display to show only first name
                    appUtils.updateUserDisplayName('currentUserName', {
                        role: 'manager',
                        name: currentManager.name
                    });

                    // Show welcome message
                    appUtils.showWelcomeMessage({
                        role: 'manager',
                        name: currentManager.name
                    });
                }
            } catch (error) {
                console.error('Error loading user name:', error);
            }
        }

        // Load appraisal data
        async function loadAppraisalData(employeeCode, periodId) {
            try {
                // Load employee data
                const { data: employee, error: empError } = await supabaseClient
                    .from('employees')
                    .select('*')
                    .eq('code_number', employeeCode)
                    .single();

                if (empError) throw empError;
                currentEmployee = employee;

                // Load period data
                const { data: period, error: periodError } = await supabaseClient
                    .from('appraisal_periods')
                    .select('*')
                    .eq('id', periodId)
                    .single();

                if (periodError) throw periodError;
                currentPeriod = period;

                // Load assigned KPIs with categories
                const { data: kpis, error: kpiError } = await supabaseClient
                    .from('employee_kpis')
                    .select(`
                        *,
                        kpi:kpis(
                            *,
                            category:kpi_categories(*)
                        )
                    `)
                    .eq('employee_code', employeeCode)
                    .order('created_at');

                if (kpiError) throw kpiError;
                assignedKPIs = kpis || [];

                console.log('Loaded KPIs:', assignedKPIs);

                // Populate UI
                populateEmployeeInfo();
                populatePeriodInfo();
                populateKPISection();

            } catch (error) {
                console.error('Error loading appraisal data:', error);
                showNotification('Error loading appraisal data: ' + error.message, 'error');
            }
        }

        // Populate employee information
        function populateEmployeeInfo() {
            document.getElementById('employeeName').textContent = currentEmployee.name;
            document.getElementById('employeePosition').textContent = currentEmployee.position;
            document.getElementById('employeeDepartment').textContent = currentEmployee.department;
        }

        // Populate period information
        function populatePeriodInfo() {
            document.getElementById('periodName').textContent = currentPeriod.name;
            const startDate = new Date(currentPeriod.start_date).toLocaleDateString();
            const endDate = new Date(currentPeriod.end_date).toLocaleDateString();
            document.getElementById('periodDuration').textContent = `${startDate} - ${endDate}`;
        }

        // Populate KPI scoring section
        function populateKPISection() {
            const container = document.getElementById('kpiScoringSection');
            container.innerHTML = '';

            if (assignedKPIs.length === 0) {
                container.innerHTML = '<p class="text-muted">No KPIs assigned to this employee.</p>';
                return;
            }

            // Group KPIs by category
            const performanceKPIs = assignedKPIs.filter(k => k.kpi && k.kpi.category && k.kpi.category.name === 'Performance');
            const behavioralKPIs = assignedKPIs.filter(k => k.kpi && k.kpi.category && k.kpi.category.name === 'Behavioral');

            console.log('Performance KPIs:', performanceKPIs);
            console.log('Behavioral KPIs:', behavioralKPIs);

            // Create Performance KPIs section
            if (performanceKPIs.length > 0) {
                const perfSection = createKPISection('Performance KPIs', performanceKPIs);
                container.appendChild(perfSection);
            }

            // Create Behavioral KPIs section
            if (behavioralKPIs.length > 0) {
                const behSection = createKPISection('Behavioral KPIs', behavioralKPIs);
                container.appendChild(behSection);
            }

            // Add event handlers for score buttons
            addScoreButtonHandlers();
        }

        // Create KPI section
        function createKPISection(title, kpis) {
            const section = document.createElement('div');
            section.className = 'mb-4';

            section.innerHTML = `
                <h5 class="category-header">${title}</h5>
                <div class="kpi-list">
                    ${kpis.map(kpi => `
                        <div class="kpi-item border rounded p-3 mb-4" data-kpi-id="${kpi.id}">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6 class="text-primary mb-2">${kpi.kpi.name}</h6>
                                    <div class="mb-2">
                                        <small class="text-muted fw-bold">Weight:</small>
                                        <span class="badge bg-secondary">${kpi.weight}%</span>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted fw-bold">Description:</small>
                                        <p class="mb-1 small text-justify">${kpi.kpi.description ? kpi.kpi.description.replace(/\n/g, '<br>') : ''}</p>
                                    </div>
                                    ${kpi.measurement ?
                                        `<div class="mb-0">
                                            <small class="text-muted fw-bold">Custom Measurement:</small>
                                            <p class="mb-0 small text-justify">${kpi.measurement.replace(/\n/g, '<br>')}</p>
                                         </div>` : ''}
                                </div>
                                <div class="col-md-8">
                                    <label class="form-label fw-bold mb-3">Select Performance Score:</label>
                                    <div class="score-buttons" data-kpi-id="${kpi.id}">
                                        <button type="button" class="btn score-btn" data-score="1" data-kpi-id="${kpi.id}">
                                            <span class="score-number">1</span>
                                            <span class="score-label">Poor</span>
                                        </button>
                                        <button type="button" class="btn score-btn" data-score="2" data-kpi-id="${kpi.id}">
                                            <span class="score-number">2</span>
                                            <span class="score-label">Need Improvement</span>
                                        </button>
                                        <button type="button" class="btn score-btn" data-score="3" data-kpi-id="${kpi.id}">
                                            <span class="score-number">3</span>
                                            <span class="score-label">Meet Requirements</span>
                                        </button>
                                        <button type="button" class="btn score-btn" data-score="4" data-kpi-id="${kpi.id}">
                                            <span class="score-number">4</span>
                                            <span class="score-label">Very Good</span>
                                        </button>
                                        <button type="button" class="btn score-btn" data-score="5" data-kpi-id="${kpi.id}">
                                            <span class="score-number">5</span>
                                            <span class="score-label">Excellent</span>
                                        </button>
                                    </div>
                                    <div class="selected-score" data-kpi-id="${kpi.id}" style="display: none;">
                                        <div class="alert alert-info py-2">
                                            <strong>Selected Score: </strong>
                                            <span class="selected-score-text"></span>
                                            <span class="selected-score-percentage"></span>
                                        </div>
                                    </div>
                                    <div class="kpi-comments">
                                        <label class="form-label">Comments (Optional)</label>
                                        <textarea class="form-control comments-input"
                                                  data-kpi-id="${kpi.id}"
                                                  placeholder="Add comments about this KPI performance..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            return section;
        }

        // Add score button event handlers
        function addScoreButtonHandlers() {
            document.querySelectorAll('.score-btn').forEach(button => {
                button.addEventListener('click', async function() {
                    const kpiId = this.getAttribute('data-kpi-id');
                    const score = parseInt(this.getAttribute('data-score'));

                    // Remove selected class from all buttons for this KPI
                    document.querySelectorAll(`.score-btn[data-kpi-id="${kpiId}"]`).forEach(btn => {
                        btn.classList.remove('selected');
                    });

                    // Add selected class to clicked button
                    this.classList.add('selected');

                    // Update selected score display
                    updateSelectedScoreDisplay(kpiId, score);

                    // Calculate and update totals
                    await calculateTotalScores();
                });
            });
        }

        // Update selected score display
        function updateSelectedScoreDisplay(kpiId, score) {
            const scoreDisplay = document.querySelector(`.selected-score[data-kpi-id="${kpiId}"]`);
            const scoreText = scoreDisplay.querySelector('.selected-score-text');
            const scorePercentage = scoreDisplay.querySelector('.selected-score-percentage');

            const gradeLabels = {
                1: 'Poor',
                2: 'Need Improvement',
                3: 'Meet Requirements',
                4: 'Very Good',
                5: 'Excellent'
            };

            scoreText.textContent = `Selected Score: ${score}/5 - ${gradeLabels[score]}`;
            scorePercentage.textContent = '';  // Remove confusing percentage display
            scoreDisplay.style.display = 'block';
        }

        // Calculate total scores
        async function calculateTotalScores() {
            // Get category weights for this employee
            const { data: categoryWeights, error: categoryError } = await supabaseClient
                .from('category_weights')
                .select(`
                    weight,
                    category:kpi_categories(name)
                `)
                .eq('employee_code', currentEmployee.code_number);

            if (categoryError) {
                console.error('Error loading category weights:', categoryError);
                return;
            }

            const performanceCategoryWeight = categoryWeights.find(cw => cw.category.name === 'Performance')?.weight || 80;
            const behavioralCategoryWeight = categoryWeights.find(cw => cw.category.name === 'Behavioral')?.weight || 20;
            let performanceWeightedScore = 0;
            let behavioralWeightedScore = 0;
            let performanceWeight = 0;
            let behavioralWeight = 0;

            assignedKPIs.forEach(kpi => {
                const selectedBtn = document.querySelector(`.score-btn[data-kpi-id="${kpi.id}"].selected`);
                if (selectedBtn) {
                    const score = parseInt(selectedBtn.getAttribute('data-score'));
                    const weight = kpi.weight;
                    // Calculate weighted score (score × weight)
                    const weightedScore = score * (weight / 100);
                    console.log(`KPI: ${kpi.kpi.name}, Score: ${score}, Weight: ${weight}%, Weighted Score: ${weightedScore}`);

                    if (kpi.kpi.category && kpi.kpi.category.name === 'Performance') {
                        performanceWeightedScore += weightedScore;
                        performanceWeight += weight;
                    } else if (kpi.kpi.category && kpi.kpi.category.name === 'Behavioral') {
                        behavioralWeightedScore += weightedScore;
                        behavioralWeight += weight;
                    }
                }
            });

            // Calculate ACTUAL category percentages (sum of weighted scores / 5 * 100)
            const performancePercentage = performanceWeight > 0 ? (performanceWeightedScore / 5) * 100 : 0;
            const behavioralPercentage = behavioralWeight > 0 ? (behavioralWeightedScore / 5) * 100 : 0;

            console.log(`Performance: weighted=${performanceWeightedScore}, actual%=${performancePercentage}%`);
            console.log(`Behavioral: weighted=${behavioralWeightedScore}, actual%=${behavioralPercentage}%`);

            // Apply category weights ONLY for total calculation
            const weightedPerformance = (performancePercentage * performanceCategoryWeight) / 100;
            const weightedBehavioral = (behavioralPercentage * behavioralCategoryWeight) / 100;
            const totalPercentage = weightedPerformance + weightedBehavioral;

            console.log(`Weighted: Performance=${weightedPerformance}%, Behavioral=${weightedBehavioral}%, Total=${totalPercentage}%`);

            // Update display
            document.getElementById('performanceScore').textContent = `${performancePercentage.toFixed(1)}%`;
            document.getElementById('behavioralScore').textContent = `${behavioralPercentage.toFixed(1)}%`;
            document.getElementById('totalScore').textContent = `${totalPercentage.toFixed(1)}%`;

            // Calculate final grade
            const finalGrade = getFinalGrade(totalPercentage);
            document.getElementById('finalGrade').textContent = finalGrade;

            // Show summary if any scores are selected
            const hasScores = document.querySelectorAll('.score-btn.selected').length > 0;
            document.getElementById('totalScoreSummary').style.display = hasScores ? 'block' : 'none';
        }

        // Get final grade based on total percentage
        function getFinalGrade(percentage) {
            if (percentage >= 90) return 'Excellent';
            if (percentage >= 80) return 'Very Good';
            if (percentage >= 70) return 'Meet Requirements';
            if (percentage >= 60) return 'Need Improvement';
            return 'Poor';
        }

        // Save appraisal
        async function saveAppraisal() {
            try {
                // Get category weights for this employee
                const { data: categoryWeights, error: categoryError } = await supabaseClient
                    .from('category_weights')
                    .select(`
                        weight,
                        category:kpi_categories(name)
                    `)
                    .eq('employee_code', currentEmployee.code_number);

                if (categoryError) throw categoryError;

                const performanceCategoryWeight = categoryWeights.find(cw => cw.category.name === 'Performance')?.weight || 80;
                const behavioralCategoryWeight = categoryWeights.find(cw => cw.category.name === 'Behavioral')?.weight || 20;

                // Validate scores
                const scores = [];

                // Calculate category scores (each category calculated as 100%)
                let performanceWeightedScore = 0;
                let behavioralWeightedScore = 0;
                let performanceKpiWeight = 0;
                let behavioralKpiWeight = 0;

                // Collect all scores
                for (const kpi of assignedKPIs) {
                    const selectedBtn = document.querySelector(`.score-btn[data-kpi-id="${kpi.id}"].selected`);
                    const commentsInput = document.querySelector(`textarea.comments-input[data-kpi-id="${kpi.id}"]`);

                    if (!selectedBtn) {
                        showNotification(`Please select a score for ${kpi.kpi.name}`, 'error');
                        document.querySelector(`.score-btn[data-kpi-id="${kpi.id}"]`).scrollIntoView();
                        return;
                    }

                    const score = parseInt(selectedBtn.getAttribute('data-score'));
                    const comments = commentsInput ? commentsInput.value.trim() : '';

                    // Calculate weighted score (score × weight)
                    const weightedScore = score * (kpi.weight / 100);

                    if (kpi.kpi.category && kpi.kpi.category.name === 'Performance') {
                        performanceWeightedScore += weightedScore;
                        performanceKpiWeight += kpi.weight;
                    } else if (kpi.kpi.category && kpi.kpi.category.name === 'Behavioral') {
                        behavioralWeightedScore += weightedScore;
                        behavioralKpiWeight += kpi.weight;
                    }

                    scores.push({
                        employee_kpi_id: kpi.id,  // CRITICAL: This is the employee_kpi ID for foreign key
                        kpi_name: kpi.kpi.name,
                        kpi_description: kpi.kpi.description,
                        kpi_category_name: kpi.kpi.category ? kpi.kpi.category.name : 'Unknown',
                        kpi_weight: kpi.weight,
                        kpi_measurement: kpi.measurement,
                        score: score,
                        comments: comments
                    });
                }

                // Calculate ACTUAL category percentages (sum of weighted scores / 5 * 100)
                const performancePercentage = performanceKpiWeight > 0 ? (performanceWeightedScore / 5) * 100 : 0;
                const behavioralPercentage = behavioralKpiWeight > 0 ? (behavioralWeightedScore / 5) * 100 : 0;

                // Apply category weights ONLY for total score calculation
                const weightedPerformance = (performancePercentage * performanceCategoryWeight) / 100;
                const weightedBehavioral = (behavioralPercentage * behavioralCategoryWeight) / 100;
                const totalScore = weightedPerformance + weightedBehavioral;
                const grade = getGrade(totalScore);

                // Get manager comments
                const managerComments = document.getElementById('managerComments').value.trim();

                // Get current manager
                const currentManager = appAuth.getCurrentEmployee();

                // Create appraisal record
                const appraisalData = {
                    employee_code: currentEmployee.code_number,
                    manager_code: currentManager.code_number,
                    period_id: currentPeriod.id,
                    performance_score: Math.round(performancePercentage * 10) / 10,
                    behavioral_score: Math.round(behavioralPercentage * 10) / 10,
                    total_score: Math.round(totalScore * 10) / 10,
                    grade: grade,
                    manager_comments: managerComments,
                    manager_signature: null,  // No auto-signature
                    manager_signature_date: null,  // No auto-signature date
                    employee_signature: null,
                    employee_signature_date: null,
                    employee_signature_requested: false,
                    created_at: new Date().toISOString()
                };

                console.log('Creating appraisal with data:', appraisalData);

                const { data: appraisal, error: appraisalError } = await supabaseClient
                    .from('appraisals')
                    .insert(appraisalData)
                    .select()
                    .single();

                if (appraisalError) throw appraisalError;

                // Create appraisal scores (matching exact schema structure)
                const scoreRecords = scores.map(score => ({
                    appraisal_id: appraisal.id,
                    employee_kpi_id: score.employee_kpi_id,  // CRITICAL: Foreign key for view compatibility
                    kpi_name: score.kpi_name,
                    kpi_description: score.kpi_description,
                    kpi_category_name: score.kpi_category_name,
                    kpi_weight: score.kpi_weight,
                    kpi_measurement: score.kpi_measurement,
                    score: score.score,
                    comments: score.comments
                }));

                const { error: scoresError } = await supabaseClient
                    .from('appraisal_scores')
                    .insert(scoreRecords);

                if (scoresError) throw scoresError;

                // Remove the assignment (appraisal completed)
                const { error: assignmentError } = await supabaseClient
                    .from('appraisal_assignments')
                    .delete()
                    .eq('employee_code', currentEmployee.code_number)
                    .eq('period_id', currentPeriod.id);

                if (assignmentError) {
                    console.warn('Warning: Could not remove assignment:', assignmentError);
                }

                showNotification('Appraisal created successfully! Please review and sign to complete.', 'success');

                // Redirect to view the created appraisal
                setTimeout(() => {
                    window.location.href = `view-appraisal.html?id=${appraisal.id}`;
                }, 1500);

            } catch (error) {
                console.error('Error saving appraisal:', error);
                showNotification('Error saving appraisal: ' + error.message, 'error');
            }
        }


    </script>
</body>
</html>
