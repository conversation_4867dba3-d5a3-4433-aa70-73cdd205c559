<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee KPIs - HR Performance Evaluation System</title>
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="icon" type="image/png" sizes="96x96" href="../assets/icons/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" sizes="any" href="../assets/icons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="../assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="../assets/icons/web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../assets/icons/web-app-manifest-512x512.png">
    <link rel="manifest" href="../manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="msapplication-config" content="../assets/icons/browserconfig.xml">
    
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Remove any default body/container margins */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }

        .container-fluid {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        .kpi-card {
            border-radius: 12px;
            transition: transform 0.2s;
        }
        .kpi-card:hover {
            transform: translateY(-2px);
        }
        .category-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                Performance Evaluation System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="team.html">
                    <i class="fas fa-arrow-left"></i> Back to Team
                </a>
            </div>
        </div>
    </nav>
    <div class="container-fluid">
        <!-- Employee Info -->
        <div class="row mb-1">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-tasks"></i> Employee KPIs
                        </h4>
                    </div>
                    <div class="card-body">
                        <div id="employeeInfo">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading employee information...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- KPIs Content -->
        <div id="kpisContent">
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading KPIs...</p>
            </div>
        </div>
    </div>

    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://xsdgviorafkfvftabiqk.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhzZGd2aW9yYWZrZnZmdGFiaXFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2NzYwNjUsImV4cCI6MjA2NzI1MjA2NX0.nA6-TnCGDBUd--s_vq2T4_Jd7WZxz1mTKFSiMgH24l8';

        const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // App utilities
        const appUtils = {
            showNotification: function(message, type = 'info') {
                console.log(`${type.toUpperCase()}: ${message}`);
                alert(message);
            }
        };
    </script>

    <script>
        let currentEmployee = null;

        // Get employee code from URL
        function getEmployeeCodeFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('employee');
        }

        // Load employee information
        async function loadEmployeeInfo() {
            try {
                const employeeCode = getEmployeeCodeFromURL();
                if (!employeeCode) {
                    throw new Error('No employee code provided');
                }

                const { data: employee, error } = await supabaseClient
                    .from('employees')
                    .select('*')
                    .eq('code_number', employeeCode)
                    .single();

                if (error) throw error;
                currentEmployee = employee;

                document.getElementById('employeeInfo').innerHTML = `
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Employee Code:</strong><br>
                            <span class="text-primary">${employee.code_number}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Name:</strong><br>
                            ${employee.name}
                        </div>
                        <div class="col-md-3">
                            <strong>Position:</strong><br>
                            ${employee.position}
                        </div>
                        <div class="col-md-3">
                            <strong>Department:</strong><br>
                            ${employee.department}
                        </div>
                    </div>
                `;

                // Load KPIs
                await loadEmployeeKPIs(employeeCode);

            } catch (error) {
                console.error('Error loading employee info:', error);
                document.getElementById('employeeInfo').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error loading employee information: ${error.message}
                    </div>
                `;
            }
        }

        // Load employee KPIs
        async function loadEmployeeKPIs(employeeCode) {
            try {
                console.log('Loading KPIs for employee:', employeeCode);

                // First, let's try a simple query to see if there's any data
                const { data: simpleCheck, error: simpleError } = await supabaseClient
                    .from('employee_kpis')
                    .select('*')
                    .eq('employee_code', employeeCode)
                    .order('created_at');

                console.log('Simple check result:', simpleCheck);
                console.log('Simple check error:', simpleError);

                if (!simpleCheck || simpleCheck.length === 0) {
                    document.getElementById('kpisContent').innerHTML = `
                        <div class="text-center text-muted">
                            <i class="fas fa-info-circle fa-3x mb-3"></i>
                            <h5>No KPIs Assigned</h5>
                            <p>This employee (${employeeCode}) has no KPIs assigned yet.</p>
                        </div>
                    `;
                    return;
                }

                // Get KPI details separately
                const kpiIds = [...new Set(simpleCheck.map(k => k.kpi_id))];
                console.log('KPI IDs to fetch:', kpiIds);

                const { data: kpisData, error: kpisError } = await supabaseClient
                    .from('kpis')
                    .select(`
                        *,
                        category:kpi_categories(*)
                    `)
                    .in('id', kpiIds);

                console.log('KPIs data:', kpisData);
                console.log('KPIs error:', kpisError);

                if (kpisError) throw kpisError;

                // Create a map of KPI details
                const kpisMap = {};
                kpisData?.forEach(kpi => {
                    kpisMap[kpi.id] = kpi;
                });

                // Combine the data while preserving the original order from simpleCheck
                const kpiAssignments = simpleCheck.map(assignment => ({
                    ...assignment,
                    kpi: kpisMap[assignment.kpi_id] || null
                }));

                console.log('Final combined KPI assignments:', kpiAssignments);

                // Get category weights for this employee
                const { data: categoryWeights, error: categoryError } = await supabaseClient
                    .from('category_weights')
                    .select(`
                        *,
                        category:kpi_categories(*)
                    `)
                    .eq('employee_code', employeeCode);

                console.log('Category weights:', categoryWeights);
                if (categoryError) console.log('Category weights error:', categoryError);

                // Create category weights map
                const categoryWeightsMap = {};
                if (categoryWeights) {
                    categoryWeights.forEach(cw => {
                        if (cw.category) {
                            categoryWeightsMap[cw.category.name] = cw.weight;
                        }
                    });
                }
                console.log('Category weights map:', categoryWeightsMap);

                // Group KPIs by category
                console.log('Raw KPI assignments:', kpiAssignments);

                // Debug: Check the structure of the first assignment
                if (kpiAssignments.length > 0) {
                    console.log('First assignment structure:', kpiAssignments[0]);
                    console.log('First assignment KPI:', kpiAssignments[0].kpi);
                    if (kpiAssignments[0].kpi) {
                        console.log('First assignment KPI category_id:', kpiAssignments[0].kpi.category_id);
                        console.log('First assignment KPI category:', kpiAssignments[0].kpi.category);
                    }
                }

                const performanceKPIs = kpiAssignments.filter(kpi => kpi.kpi && kpi.kpi.category && kpi.kpi.category.name === 'Performance');
                const behavioralKPIs = kpiAssignments.filter(kpi => kpi.kpi && kpi.kpi.category && kpi.kpi.category.name === 'Behavioral');

                console.log('Performance KPIs:', performanceKPIs);
                console.log('Behavioral KPIs:', behavioralKPIs);

                let kpisHTML = '';

                // Performance KPIs
                if (performanceKPIs.length > 0) {
                    kpisHTML += generateCategorySection('Performance', performanceKPIs, 'primary');
                }

                // Behavioral KPIs
                if (behavioralKPIs.length > 0) {
                    kpisHTML += generateCategorySection('Behavioral', behavioralKPIs, 'success');
                }

                // Summary
                console.log('Calculating weights...');
                console.log('Performance KPIs for weight calc:', performanceKPIs);
                console.log('Behavioral KPIs for weight calc:', behavioralKPIs);

                const totalPerformanceWeight = performanceKPIs.reduce((sum, kpi) => {
                    console.log('Performance KPI weight:', kpi.weight, 'type:', typeof kpi.weight);
                    return sum + (kpi.weight || 0);
                }, 0);

                const totalBehavioralWeight = behavioralKPIs.reduce((sum, kpi) => {
                    console.log('Behavioral KPI weight:', kpi.weight, 'type:', typeof kpi.weight);
                    return sum + (kpi.weight || 0);
                }, 0);

                console.log('Total Performance Weight:', totalPerformanceWeight);
                console.log('Total Behavioral Weight:', totalBehavioralWeight);

                // Calculate weights manually to be sure
                let perfWeight = 0;
                let behavWeight = 0;

                console.log('=== WEIGHT CALCULATION DEBUG ===');
                performanceKPIs.forEach((kpi, index) => {
                    console.log(`Performance KPI ${index}:`, kpi);
                    console.log(`Weight value:`, kpi.weight);
                    console.log(`Weight type:`, typeof kpi.weight);
                    perfWeight += parseInt(kpi.weight) || 0;
                    console.log(`Running total:`, perfWeight);
                });

                behavioralKPIs.forEach((kpi, index) => {
                    console.log(`Behavioral KPI ${index}:`, kpi);
                    console.log(`Weight value:`, kpi.weight);
                    console.log(`Weight type:`, typeof kpi.weight);
                    behavWeight += parseInt(kpi.weight) || 0;
                    console.log(`Running total:`, behavWeight);
                });

                console.log('FINAL WEIGHTS:', perfWeight, behavWeight);

                // Get actual category weights for display
                const performanceCategoryWeight = categoryWeightsMap['Performance'] || 0;
                const behavioralCategoryWeight = categoryWeightsMap['Behavioral'] || 0;

                kpisHTML += `
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-primary">Performance KPIs</h6>
                                    <h4 class="text-primary">${performanceCategoryWeight}%</h4>
                                    <small>${performanceKPIs.length} KPIs (Total KPI Weight: ${perfWeight}%)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-success">Behavioral KPIs</h6>
                                    <h4 class="text-success">${behavioralCategoryWeight}%</h4>
                                    <small>${behavioralKPIs.length} KPIs (Total KPI Weight: ${behavWeight}%)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('kpisContent').innerHTML = kpisHTML;

            } catch (error) {
                console.error('Error loading KPIs:', error);
                document.getElementById('kpisContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error loading KPIs: ${error.message}
                    </div>
                `;
            }
        }

        // Generate category section
        function generateCategorySection(categoryName, kpis, colorClass) {
            const iconClass = categoryName === 'Performance' ? 'fas fa-chart-line' : 'fas fa-users';
            
            let html = `
                <div class="category-header">
                    <h5 class="text-${colorClass} mb-0">
                        <i class="${iconClass}"></i> ${categoryName} KPIs
                    </h5>
                </div>
                <div class="row mb-4">
            `;

            kpis.forEach(assignment => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card kpi-card border-${colorClass}">
                            <div class="card-body">
                                <h6 class="card-title text-${colorClass}">${assignment.kpi ? assignment.kpi.name : 'Unknown KPI'}</h6>
                                <p class="card-text text-muted mb-3">${assignment.kpi ? assignment.kpi.description : 'No description'}</p>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-${colorClass}">Weight: ${assignment.weight || 0}%</span>
                                    <small class="text-muted">${assignment.kpi && assignment.kpi.category ? assignment.kpi.category.name : categoryName}</small>
                                </div>
                                ${assignment.measurement ? 
                                    `<div class="mt-2 p-2 bg-light rounded">
                                        <small class="text-info">
                                            <i class="fas fa-ruler"></i> <strong>Measurement:</strong> ${assignment.measurement}
                                        </small>
                                    </div>` : ''
                                }
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `</div>`;
            return html;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadEmployeeInfo();
        });
    </script>
</body>
</html>
