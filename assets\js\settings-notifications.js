// Settings and Notifications functionality
// This file provides reusable functionality for all user accounts

// Initialize Settings and Notifications
function initializeSettingsAndNotifications() {
    // Add event listeners
    document.getElementById('settingsBtn').addEventListener('click', function() {
        document.getElementById('settingsModal').style.display = 'flex';
    });

    document.getElementById('notificationsBtn').addEventListener('click', function() {
        loadNotifications();
        document.getElementById('notificationsModal').style.display = 'flex';
    });

    // Password change form
    document.getElementById('changePasswordForm').addEventListener('submit', handlePasswordChange);

    // Load notifications on page load
    setTimeout(loadNotifications, 1000); // Load after user data is loaded
}

// Modal control functions
function closeSettingsModal() {
    document.getElementById('settingsModal').style.display = 'none';
    document.getElementById('changePasswordForm').reset();
}

function closeNotificationsModal() {
    document.getElementById('notificationsModal').style.display = 'none';
}

// Password visibility toggle
function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + 'Icon');

    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

// Password change functionality
async function handlePasswordChange(e) {
    e.preventDefault();

    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // Validate NEW password format (4-12 characters, must contain _ or @)
    const passwordRegex = /^(?=.*[_@])[a-zA-Z0-9_@]{4,12}$/;
    if (!passwordRegex.test(newPassword)) {
        appUtils.showNotification('New password must be 4-12 characters with letters, numbers, and must contain _ or @', 'error');
        return;
    }

    // Check if passwords match
    if (newPassword !== confirmPassword) {
        appUtils.showNotification('New passwords do not match', 'error');
        return;
    }

    try {
        // Get current user data using the same method as the page
        const currentEmployee = appAuth.getCurrentEmployee();
        const currentUser = appAuth.getCurrentUser();

        console.log('Current employee:', currentEmployee);
        console.log('Current user:', currentUser);

        if (!currentEmployee || !currentUser) {
            appUtils.showNotification('User session not found', 'error');
            return;
        }

        // Extract the actual username (handle both 'admin' and '<EMAIL>' formats)
        let actualUsername = currentUser.username;
        if (actualUsername && actualUsername.includes('@')) {
            actualUsername = actualUsername.split('@')[0]; // Extract 'admin' from '<EMAIL>'
        }

        console.log('Employee code:', currentEmployee.code_number);
        console.log('Employee name:', currentEmployee.name);
        console.log('Verifying password for user:', actualUsername, '(original:', currentUser.username, ')');

        // Get user data first, then verify password
        const { data: userData, error: userError } = await supabaseClient
            .from('users')
            .select('id, username, password')
            .eq('username', actualUsername)
            .single();

        console.log('User data retrieval result:', { userData, userError });

        if (userError || !userData) {
            console.error('User not found:', userError);
            appUtils.showNotification('User not found', 'error');
            return;
        }

        // Verify password in JavaScript
        console.log('Password comparison:');
        console.log('- Database password:', userData.password);
        console.log('- Entered password:', currentPassword);
        console.log('- Passwords match:', userData.password === currentPassword);

        if (userData.password !== currentPassword) {
            console.log('❌ Password mismatch!');
            appUtils.showNotification('Current password is incorrect', 'error');
            return;
        }

        console.log('✅ Password verification successful!');

        // Update password using the correct username
        const { error: updateError } = await supabaseClient
            .from('users')
            .update({ password: newPassword })
            .eq('username', actualUsername);

        if (updateError) {
            console.error('Error updating password:', updateError);
            throw updateError;
        }

        appUtils.showNotification('Password changed successfully', 'success');
        closeSettingsModal();

    } catch (error) {
        console.error('Error changing password:', error);
        appUtils.showNotification('Error changing password: ' + (error.message || 'Unknown error'), 'error');
    }
}

// Notifications functionality
async function loadNotifications() {
    try {
        const currentUser = appAuth.getCurrentUser();
        const currentEmployee = appAuth.getCurrentEmployee();
        if (!currentUser || !currentEmployee) return;
        
        let notifications = [];
        
        // Load notifications based on user role
        let allNotifications = [];

        // Always check for own appraisal needing signature (applies to all roles)
        const myAppraisalNotifications = await getMyAppraisalNotifications(currentEmployee.code_number);
        allNotifications = allNotifications.concat(myAppraisalNotifications);

        // Add role-specific notifications
        if (currentUser.role === 'manager') {
            const managerNotifications = await getManagerNotifications(currentEmployee.code_number);
            allNotifications = allNotifications.concat(managerNotifications);
        } else if (currentUser.role === 'employee') {
            const employeeNotifications = await getEmployeeNotifications(currentEmployee.code_number);
            allNotifications = allNotifications.concat(employeeNotifications);
        } else if (currentUser.role === 'admin') {
            const adminNotifications = await getAdminNotifications();
            allNotifications = allNotifications.concat(adminNotifications);
        }
        
        displayNotifications(allNotifications);
        updateNotificationBadge(allNotifications.filter(n => !n.read).length);
        
    } catch (error) {
        console.error('Error loading notifications:', error);
    }
}

// My own appraisal notifications (applies to all users)
async function getMyAppraisalNotifications(employeeCode) {
    const notifications = [];

    try {
        // Check for my own appraisals that need my signature
        const { data: myAppraisals } = await supabaseClient
            .from('appraisals')
            .select('id, employee_code, period_id, updated_at, manager_signature, employee_signature, employee_signature_requested')
            .eq('employee_code', employeeCode)
            .not('manager_signature', 'is', null)  // Manager has signed
            .is('employee_signature', null);       // But I haven't signed yet

        if (myAppraisals && myAppraisals.length > 0) {
            for (const appraisal of myAppraisals) {
                try {
                    // Get period name
                    const { data: period } = await supabaseClient
                        .from('appraisal_periods')
                        .select('name')
                        .eq('id', appraisal.period_id)
                        .single();

                    const periodName = period?.name || 'Unknown Period';

                    // Determine notification text based on signature request status
                    let notificationText;
                    if (appraisal.employee_signature_requested) {
                        notificationText = `🔔 Your appraisal for ${periodName} is ready for your signature`;
                    } else {
                        notificationText = `📝 Your manager completed your appraisal for ${periodName} - signature will be requested soon`;
                    }

                    notifications.push({
                        id: `my_appraisal_${appraisal.id}`,
                        type: 'my_signature',
                        text: notificationText,
                        time: new Date(appraisal.updated_at).toLocaleDateString(),
                        read: false
                    });
                } catch (error) {
                    console.error('Error processing my appraisal notification:', error);
                }
            }
        }
    } catch (error) {
        console.error('Error loading my appraisal notifications:', error);
    }

    return notifications.sort((a, b) => new Date(b.time) - new Date(a.time));
}

// Manager notifications
async function getManagerNotifications(managerCode) {
    // Get direct reports
    const { data: directReports } = await supabaseClient
        .from('employees')
        .select('code_number, name')
        .eq('manager_code', managerCode);
    
    if (!directReports || directReports.length === 0) return [];
    
    const reportCodes = directReports.map(emp => emp.code_number);
    const notifications = [];
    
    // Check for new appraisal assignments (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const { data: assignments } = await supabaseClient
        .from('appraisal_assignments')
        .select('id, employee_code, period_id, assigned_at')
        .in('employee_code', reportCodes)
        .gte('assigned_at', sevenDaysAgo.toISOString());
    
    if (assignments && assignments.length > 0) {
        for (const assignment of assignments) {
            try {
                // Get employee and period names
                const [employeeResult, periodResult] = await Promise.all([
                    supabaseClient.from('employees').select('name').eq('code_number', assignment.employee_code).single(),
                    supabaseClient.from('appraisal_periods').select('name').eq('id', assignment.period_id).single()
                ]);

                const employeeName = employeeResult.data?.name || 'Unknown Employee';
                const periodName = periodResult.data?.name || 'Unknown Period';

                notifications.push({
                    id: `assignment_${assignment.id}`,
                    type: 'assignment',
                    text: `New appraisal assignment for ${employeeName} - ${periodName}`,
                    time: new Date(assignment.assigned_at).toLocaleDateString(),
                    read: false
                });
            } catch (error) {
                console.error('Error processing assignment notification:', error);
            }
        }
    }
    
    // Check for completed appraisals needing manager signature
    const { data: completedAppraisals } = await supabaseClient
        .from('appraisals')
        .select('id, employee_code, period_id, updated_at, manager_signature')
        .in('employee_code', reportCodes)
        .is('manager_signature', null);
    
    if (completedAppraisals && completedAppraisals.length > 0) {
        for (const appraisal of completedAppraisals) {
            try {
                // Get employee and period names
                const [employeeResult, periodResult] = await Promise.all([
                    supabaseClient.from('employees').select('name').eq('code_number', appraisal.employee_code).single(),
                    supabaseClient.from('appraisal_periods').select('name').eq('id', appraisal.period_id).single()
                ]);

                const employeeName = employeeResult.data?.name || 'Unknown Employee';
                const periodName = periodResult.data?.name || 'Unknown Period';

                notifications.push({
                    id: `signature_${appraisal.id}`,
                    type: 'signature',
                    text: `${employeeName} completed appraisal for ${periodName} - Needs your signature`,
                    time: new Date(appraisal.updated_at).toLocaleDateString(),
                    read: false
                });
            } catch (error) {
                console.error('Error processing completed appraisal notification:', error);
            }
        }
    }
    
    return notifications.sort((a, b) => new Date(b.time) - new Date(a.time));
}

// Employee notifications
async function getEmployeeNotifications(employeeCode) {
    const notifications = [];
    
    // Check for new appraisal assignments (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const { data: assignments } = await supabaseClient
        .from('appraisal_assignments')
        .select('id, employee_code, period_id, assigned_at')
        .eq('employee_code', employeeCode)
        .gte('assigned_at', sevenDaysAgo.toISOString());
    
    if (assignments && assignments.length > 0) {
        for (const assignment of assignments) {
            try {
                // Get period name
                const { data: period } = await supabaseClient
                    .from('appraisal_periods')
                    .select('name')
                    .eq('id', assignment.period_id)
                    .single();

                const periodName = period?.name || 'Unknown Period';

                notifications.push({
                    id: `assignment_${assignment.id}`,
                    type: 'assignment',
                    text: `New appraisal assignment for ${periodName}`,
                    time: new Date(assignment.assigned_at).toLocaleDateString(),
                    read: false
                });
            } catch (error) {
                console.error('Error processing employee assignment notification:', error);
            }
        }
    }
    
    // Check for appraisals needing employee signature (manager signed but employee hasn't)
    const { data: pendingAppraisals } = await supabaseClient
        .from('appraisals')
        .select('id, employee_code, period_id, updated_at, manager_signature, employee_signature')
        .eq('employee_code', employeeCode)
        .not('manager_signature', 'is', null)
        .is('employee_signature', null);
    
    if (pendingAppraisals && pendingAppraisals.length > 0) {
        for (const appraisal of pendingAppraisals) {
            try {
                // Get period name
                const { data: period } = await supabaseClient
                    .from('appraisal_periods')
                    .select('name')
                    .eq('id', appraisal.period_id)
                    .single();

                const periodName = period?.name || 'Unknown Period';

                notifications.push({
                    id: `employee_signature_${appraisal.id}`,
                    type: 'signature',
                    text: `Your appraisal for ${periodName} needs your signature`,
                    time: new Date(appraisal.updated_at).toLocaleDateString(),
                    read: false
                });
            } catch (error) {
                console.error('Error processing employee signature notification:', error);
            }
        }
    }
    
    return notifications.sort((a, b) => new Date(b.time) - new Date(a.time));
}

// Admin notifications
async function getAdminNotifications() {
    const notifications = [];

    try {
        // Check for appraisals needing admin attention (unsigned appraisals from last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const { data: pendingAppraisals, error } = await supabaseClient
            .from('appraisals')
            .select('id, employee_code, period_id, created_at, manager_signature, employee_signature')
            .is('manager_signature', null)
            .gte('created_at', thirtyDaysAgo.toISOString());

        if (error) {
            console.error('Error loading admin notifications:', error);
            return notifications; // Return empty array on error
        }

        if (pendingAppraisals && pendingAppraisals.length > 0) {
            // Get employee and period details separately to avoid complex joins
            for (const appraisal of pendingAppraisals) {
                try {
                    // Get employee name
                    const { data: employee } = await supabaseClient
                        .from('employees')
                        .select('name')
                        .eq('code_number', appraisal.employee_code)
                        .single();

                    // Get period name
                    const { data: period } = await supabaseClient
                        .from('appraisal_periods')
                        .select('name')
                        .eq('id', appraisal.period_id)
                        .single();

                    if (employee && period) {
                        notifications.push({
                            id: `pending_${appraisal.id}`,
                            type: 'pending',
                            text: `Pending appraisal for ${employee.name} - ${period.name}`,
                            time: new Date(appraisal.created_at).toLocaleDateString(),
                            read: false
                        });
                    }
                } catch (detailError) {
                    console.error('Error getting appraisal details:', detailError);
                    // Add notification without details if we can't get them
                    notifications.push({
                        id: `pending_${appraisal.id}`,
                        type: 'pending',
                        text: `Pending appraisal (ID: ${appraisal.id})`,
                        time: new Date(appraisal.created_at).toLocaleDateString(),
                        read: false
                    });
                }
            }
        }
    } catch (error) {
        console.error('Error in getAdminNotifications:', error);
    }

    return notifications.sort((a, b) => new Date(b.time) - new Date(a.time));
}

// Display notifications
function displayNotifications(notifications) {
    const notificationsList = document.getElementById('notificationsList');
    
    if (notifications.length === 0) {
        notificationsList.innerHTML = `
            <div class="empty-notifications">
                <i class="fas fa-bell-slash fa-2x mb-2"></i>
                <p>No notifications</p>
            </div>
        `;
        return;
    }
    
    notificationsList.innerHTML = notifications.map(notification => `
        <div class="notification-item ${notification.read ? '' : 'unread'}">
            <div class="notification-text">${notification.text}</div>
            <div class="notification-time">${notification.time}</div>
        </div>
    `).join('');
}

// Update notification badge
function updateNotificationBadge(count) {
    const badge = document.getElementById('notificationBadge');
    if (count > 0) {
        badge.textContent = count > 99 ? '99+' : count;
        badge.style.display = 'flex';
    } else {
        badge.style.display = 'none';
    }
}

// Mark all as read
function markAllAsRead() {
    updateNotificationBadge(0);
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => item.classList.remove('unread'));
    appUtils.showNotification('All notifications marked as read', 'success');
}

// Clear all notifications
function clearAllNotifications() {
    document.getElementById('notificationsList').innerHTML = `
        <div class="empty-notifications">
            <i class="fas fa-bell-slash fa-2x mb-2"></i>
            <p>No notifications</p>
        </div>
    `;
    updateNotificationBadge(0);
    appUtils.showNotification('All notifications cleared', 'success');
}
