-- ============================================================================
-- RLS POLICIES FOR PERFORMANCE MANAGEMENT SYSTEM
-- ============================================================================
-- This script enables Row Level Security (RLS) and creates policies that
-- replicate the existing frontend access control logic at the database level.
--
-- ROLES:
-- - admin: Full access to all tables and operations
-- - manager: Access to their team + extended team hierarchy
-- - employee: Access only to their own data
--
-- IMPORTANT: Run this script manually in Supabase SQL Editor
-- ============================================================================

-- Create helper functions for role detection and hierarchy checking
-- ============================================================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role 
        FROM users 
        WHERE username = current_setting('app.current_user', true)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's employee code
CREATE OR REPLACE FUNCTION get_current_user_employee_code()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT employee_code 
        FROM users 
        WHERE username = current_setting('app.current_user', true)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN get_current_user_role() = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is manager
CREATE OR REPLACE FUNCTION is_manager()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN get_current_user_role() IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all employee codes in user's team hierarchy (including extended team)
CREATE OR REPLACE FUNCTION get_user_team_hierarchy()
RETURNS TEXT[] AS $$
DECLARE
    user_employee_code TEXT;
    team_codes TEXT[];
BEGIN
    user_employee_code := get_current_user_employee_code();
    
    -- If admin, return empty array (admin has access to all)
    IF is_admin() THEN
        RETURN ARRAY[]::TEXT[];
    END IF;
    
    -- Get all employees in the hierarchy recursively
    WITH RECURSIVE team_hierarchy AS (
        -- Start with direct reports
        SELECT code_number
        FROM employees
        WHERE manager_code = user_employee_code
        
        UNION ALL
        
        -- Recursively get reports of reports
        SELECT e.code_number
        FROM employees e
        INNER JOIN team_hierarchy th ON e.manager_code = th.code_number
    )
    SELECT ARRAY(
        SELECT code_number FROM team_hierarchy
        UNION
        SELECT user_employee_code  -- Include the user themselves
    ) INTO team_codes;
    
    RETURN COALESCE(team_codes, ARRAY[user_employee_code]);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- ENABLE RLS ON ALL TABLES
-- ============================================================================

ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE kpi_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE kpis ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_kpis ENABLE ROW LEVEL SECURITY;
ALTER TABLE category_weights ENABLE ROW LEVEL SECURITY;
ALTER TABLE appraisal_periods ENABLE ROW LEVEL SECURITY;
ALTER TABLE appraisals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appraisal_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE appraisal_assignments ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- EMPLOYEES TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_employees_all" ON employees
    FOR ALL USING (is_admin());

-- Manager: Can view/edit their team hierarchy
CREATE POLICY "manager_employees_team" ON employees
    FOR ALL USING (
        is_manager() AND (
            code_number = ANY(get_user_team_hierarchy())
        )
    );

-- Employee: Can view their own record only
CREATE POLICY "employee_employees_own" ON employees
    FOR SELECT USING (
        code_number = get_current_user_employee_code()
    );

-- ============================================================================
-- USERS TABLE POLICIES
-- ============================================================================

-- Admin: Full access to manage all users
CREATE POLICY "admin_users_all" ON users
    FOR ALL USING (is_admin());

-- Manager: Can view users in their team hierarchy
CREATE POLICY "manager_users_team" ON users
    FOR SELECT USING (
        is_manager() AND (
            employee_code = ANY(get_user_team_hierarchy())
        )
    );

-- All users: Can update their own password
CREATE POLICY "users_update_own_password" ON users
    FOR UPDATE USING (
        username = current_setting('app.current_user', true)
    )
    WITH CHECK (
        username = current_setting('app.current_user', true)
    );

-- Employee: Can view their own user record
CREATE POLICY "employee_users_own" ON users
    FOR SELECT USING (
        username = current_setting('app.current_user', true)
    );

-- ============================================================================
-- KPI_CATEGORIES TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_kpi_categories_all" ON kpi_categories
    FOR ALL USING (is_admin());

-- Manager/Employee: Read-only access
CREATE POLICY "users_kpi_categories_read" ON kpi_categories
    FOR SELECT USING (true);

-- ============================================================================
-- KPIS TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_kpis_all" ON kpis
    FOR ALL USING (is_admin());

-- Manager/Employee: Read-only access
CREATE POLICY "users_kpis_read" ON kpis
    FOR SELECT USING (true);

-- ============================================================================
-- EMPLOYEE_KPIS TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_employee_kpis_all" ON employee_kpis
    FOR ALL USING (is_admin());

-- Manager: Can view/edit KPIs for their team hierarchy
CREATE POLICY "manager_employee_kpis_team" ON employee_kpis
    FOR ALL USING (
        is_manager() AND (
            employee_code = ANY(get_user_team_hierarchy())
        )
    );

-- Employee: Can view their own KPIs only
CREATE POLICY "employee_employee_kpis_own" ON employee_kpis
    FOR SELECT USING (
        employee_code = get_current_user_employee_code()
    );

-- ============================================================================
-- CATEGORY_WEIGHTS TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_category_weights_all" ON category_weights
    FOR ALL USING (is_admin());

-- Manager: Can view/edit weights for their team hierarchy
CREATE POLICY "manager_category_weights_team" ON category_weights
    FOR ALL USING (
        is_manager() AND (
            employee_code = ANY(get_user_team_hierarchy())
        )
    );

-- Employee: Can view their own category weights only
CREATE POLICY "employee_category_weights_own" ON category_weights
    FOR SELECT USING (
        employee_code = get_current_user_employee_code()
    );

-- ============================================================================
-- APPRAISAL_PERIODS TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_appraisal_periods_all" ON appraisal_periods
    FOR ALL USING (is_admin());

-- Manager/Employee: Read-only access to all periods
CREATE POLICY "users_appraisal_periods_read" ON appraisal_periods
    FOR SELECT USING (true);

-- ============================================================================
-- APPRAISALS TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_appraisals_all" ON appraisals
    FOR ALL USING (is_admin());

-- Manager: Can view/edit appraisals for their team hierarchy
CREATE POLICY "manager_appraisals_team" ON appraisals
    FOR ALL USING (
        is_manager() AND (
            employee_code = ANY(get_user_team_hierarchy()) OR
            manager_code = get_current_user_employee_code()
        )
    );

-- Employee: Can view their own appraisals and update signature fields
CREATE POLICY "employee_appraisals_own_view" ON appraisals
    FOR SELECT USING (
        employee_code = get_current_user_employee_code()
    );

CREATE POLICY "employee_appraisals_own_signature" ON appraisals
    FOR UPDATE USING (
        employee_code = get_current_user_employee_code()
    )
    WITH CHECK (
        employee_code = get_current_user_employee_code()
    );

-- ============================================================================
-- APPRAISAL_SCORES TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_appraisal_scores_all" ON appraisal_scores
    FOR ALL USING (is_admin());

-- Manager: Can view/edit scores for appraisals in their team hierarchy
CREATE POLICY "manager_appraisal_scores_team" ON appraisal_scores
    FOR ALL USING (
        is_manager() AND EXISTS (
            SELECT 1 FROM appraisals a
            WHERE a.id = appraisal_id
            AND (
                a.employee_code = ANY(get_user_team_hierarchy()) OR
                a.manager_code = get_current_user_employee_code()
            )
        )
    );

-- Employee: Can view scores for their own appraisals only
CREATE POLICY "employee_appraisal_scores_own" ON appraisal_scores
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM appraisals a
            WHERE a.id = appraisal_id
            AND a.employee_code = get_current_user_employee_code()
        )
    );

-- ============================================================================
-- APPRAISAL_ASSIGNMENTS TABLE POLICIES
-- ============================================================================

-- Admin: Full access
CREATE POLICY "admin_appraisal_assignments_all" ON appraisal_assignments
    FOR ALL USING (is_admin());

-- Manager: Can view/edit assignments for their team hierarchy
CREATE POLICY "manager_appraisal_assignments_team" ON appraisal_assignments
    FOR ALL USING (
        is_manager() AND (
            employee_code = ANY(get_user_team_hierarchy()) OR
            assigned_by = get_current_user_employee_code()
        )
    );

-- Employee: Can view their own assignments only
CREATE POLICY "employee_appraisal_assignments_own" ON appraisal_assignments
    FOR SELECT USING (
        employee_code = get_current_user_employee_code()
    );

-- ============================================================================
-- GRANT NECESSARY PERMISSIONS
-- ============================================================================

-- Grant usage on functions to authenticated users
GRANT EXECUTE ON FUNCTION get_current_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_user_employee_code() TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION is_manager() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_team_hierarchy() TO authenticated;

-- ============================================================================
-- RLS ACTIVATION COMPLETE
-- ============================================================================
--
-- IMPORTANT NOTES:
-- 1. Your frontend must set the current user context using:
--    SET app.current_user = 'username';
--
-- 2. This should be done after user authentication in your application
--
-- 3. All existing functionality will work exactly the same
--
-- 4. To disable RLS if needed, run:
--    ALTER TABLE table_name DISABLE ROW LEVEL SECURITY;
--    for each table
--
-- 5. Test thoroughly with different user roles before production use
-- ============================================================================
