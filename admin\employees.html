<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Management - HR Performance Evaluation System</title>
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="icon" type="image/png" sizes="96x96" href="../assets/icons/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" sizes="any" href="../assets/icons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="../assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="../assets/icons/web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../assets/icons/web-app-manifest-512x512.png">
    <link rel="manifest" href="../manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="msapplication-config" content="../assets/icons/browserconfig.xml">
    
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- SheetJS (XLSX) -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern color palette for charts */
        :root {
            --chart-primary: #4a90e2;
            --chart-secondary: #7bb3f0;
            --chart-success: #27ae60;
            --chart-info: #3498db;
            --chart-warning: #f39c12;
            --chart-danger: #e74c3c;
        }

        /* Interactive filter button styles */
        .filter-btn-active {
            background-color: #e74c3c !important;
            border-color: #e74c3c !important;
            color: white !important;
        }

        /* Modern card styling */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Filter section styling */
        .filter-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        /* Dark mode support */
        body.dark-mode .filter-section {
            background: #2d2d2d;
            border: 1px solid #444444;
            color: #e0e0e0;
        }

        body.dark-mode .card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            border-color: #333333;
            color: white;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3, body.dark-mode h5 {
            color: #e0e0e0;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="employees.html" class="nav-link active">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="kpis.html" class="nav-link">
                    <i class="fas fa-bullseye"></i> KPIs
                </a>
                <a href="assign-kpis.html" class="nav-link">
                    <i class="fas fa-user-tag"></i> Assign KPIs
                </a>
                <a href="assign-appraisals.html" class="nav-link">
                    <i class="fas fa-clipboard-list"></i> Assign Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Employee Management</h1>
                <button id="addEmployeeBtn" class="btn btn-primary">
                    <i class="fas fa-plus mr-1"></i> Add Employee
                </button>
            </div>
            
            <!-- Filter controls -->
            <div class="card mb-3 filter-section">
                <div class="card-body py-2">
                    <div class="row g-2">
                        <div class="col-md-3">
                            <label for="departmentFilter" class="form-label small">Department</label>
                            <select id="departmentFilter" class="form-select form-select-sm">
                                <option value="">All Departments</option>
                                <!-- Departments will be loaded dynamically -->
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="positionFilter" class="form-label small">Position</label>
                            <select id="positionFilter" class="form-select form-select-sm">
                                <option value="">All Positions</option>
                                <!-- Positions will be loaded dynamically -->
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="managerFilter" class="form-label small">Manager</label>
                            <select id="managerFilter" class="form-select form-select-sm">
                                <option value="">All Managers</option>
                                <!-- Managers will be loaded dynamically -->
                            </select>
                        </div>

                        <div class="col-md-3 d-flex align-items-end">
                            <button id="applyFilters" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-filter"></i> Apply
                            </button>
                            <button id="resetFilters" class="btn btn-secondary btn-sm me-2">
                                <i class="fas fa-times"></i> Reset
                            </button>
                            <button id="exportEmployees" class="btn btn-success btn-sm">
                                <i class="fas fa-download me-1"></i> Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Employees table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="employeesTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Manager</th>
                                    <th>Is Manager</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="employeesTableBody">
                                <!-- Employees will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Add/Edit Employee Modal -->
    <div id="employeeModal" class="modal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">Add Employee</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="employeeForm">
                        <input type="hidden" id="employeeId">
                        
                        <div class="form-group">
                            <label for="codeNumber" class="form-label">Code Number</label>
                            <input type="text" id="codeNumber" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" id="name" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="position" class="form-label">Position</label>
                            <input type="text" id="position" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="department" class="form-label">Department</label>
                            <input type="text" id="department" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="managerId" class="form-label">Manager</label>
                            <select id="managerId" class="form-select">
                                <option value="">No Manager</option>
                                <!-- Managers will be loaded dynamically -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">User Role (Select One)</label>
                            <div class="form-check">
                                <input type="checkbox" id="isAdmin" class="form-check-input role-checkbox">
                                <label for="isAdmin" class="form-check-label">Is Admin</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="isManager" class="form-check-input role-checkbox">
                                <label for="isManager" class="form-check-label">Is Manager</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="isEmployee" class="form-check-input role-checkbox">
                                <label for="isEmployee" class="form-check-label">Is Employee</label>
                            </div>
                        </div>

                        <div id="userAccountSection" style="display: none;">
                            <h4 class="mt-4 mb-3">User Account</h4>

                            <div class="form-group">
                                <label for="username" class="form-label">Username <span id="usernameRequired" class="text-danger">*</span></label>
                                <input type="text" id="username" class="form-control">
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">Password <span id="passwordRequired" class="text-danger">*</span></label>
                                <input type="password" id="password" class="form-control">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="button" id="saveEmployee" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirm Delete</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this employee? This action cannot be undone.</p>
                    <p id="deleteEmployeeName" class="font-bold"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="button" id="confirmDelete" class="btn btn-danger">Delete</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('admin')) {
                return;
            }

            // Load current user name
            loadCurrentUserName();

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Load employees
            loadEmployees();
            
            // Load filters
            loadFilters();
            
            // Apply filters button
            document.getElementById('applyFilters').addEventListener('click', function() {
                // Add active state to apply button
                this.classList.add('filter-btn-active');
                document.getElementById('resetFilters').classList.remove('filter-btn-active');
                loadEmployees();
            });

            // Reset filters button
            document.getElementById('resetFilters').addEventListener('click', function() {
                // Reset filter values
                document.getElementById('departmentFilter').value = '';
                document.getElementById('positionFilter').value = '';
                document.getElementById('managerFilter').value = '';

                // Remove active state from apply button and restore normal state
                document.getElementById('applyFilters').classList.remove('filter-btn-active');
                this.classList.remove('filter-btn-active');

                loadEmployees();
            });
            
            // Export to Excel button
            document.getElementById('exportEmployees').addEventListener('click', function() {
                appUtils.exportToExcel('employeesTable', 'Employees_List');
            });
            
            // Add Employee button
            document.getElementById('addEmployeeBtn').addEventListener('click', function() {
                openEmployeeModal();
            });
            
            // Role checkboxes - only one can be selected at a time
            document.querySelectorAll('.role-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        // Uncheck all other role checkboxes
                        document.querySelectorAll('.role-checkbox').forEach(cb => {
                            if (cb !== this) cb.checked = false;
                        });

                        // Show user account section
                        document.getElementById('userAccountSection').style.display = 'block';

                        // Update required indicators based on role
                        const usernameRequired = document.getElementById('usernameRequired');
                        const passwordRequired = document.getElementById('passwordRequired');

                        if (this.id === 'isEmployee') {
                            // Optional for employees
                            usernameRequired.style.display = 'none';
                            passwordRequired.style.display = 'none';
                        } else {
                            // Required for admin and manager
                            usernameRequired.style.display = 'inline';
                            passwordRequired.style.display = 'inline';
                        }
                    } else {
                        // If unchecked and no other role is selected, hide user account section
                        const anyRoleSelected = document.querySelectorAll('.role-checkbox:checked').length > 0;
                        if (!anyRoleSelected) {
                            document.getElementById('userAccountSection').style.display = 'none';
                        }
                    }
                });
            });
            
            // Save Employee button
            document.getElementById('saveEmployee').addEventListener('click', function() {
                saveEmployee();
            });
            
            // Confirm Delete button
            document.getElementById('confirmDelete').addEventListener('click', function() {
                deleteEmployee();
            });
            
            // Close modal buttons
            document.querySelectorAll('.close-modal').forEach(button => {
                button.addEventListener('click', function() {
                    closeModals();
                });
            });
        });
        
        // Load employees
        async function loadEmployees() {
            try {
                console.log('Admin employees: Loading employees...');
                // Get filter values
                const departmentFilter = document.getElementById('departmentFilter').value;
                const positionFilter = document.getElementById('positionFilter').value;
                const managerFilter = document.getElementById('managerFilter').value;
                
                // Build query
                let query = supabaseClient
                    .from('employees')
                    .select('*')
                    .order('name');
                
                if (departmentFilter) {
                    query = query.eq('department', departmentFilter);
                }
                
                if (positionFilter) {
                    query = query.eq('position', positionFilter);
                }
                
                if (managerFilter) {
                    query = query.eq('manager_code', managerFilter);
                }
                
                // Execute query
                const { data: employees, error } = await query;
                
                if (error) throw error;
                
                // Get manager names for each employee
                const employeesWithManagers = await Promise.all(
                    (employees || []).map(async (employee) => {
                        if (employee.manager_code) {
                            const { data: manager } = await supabaseClient
                                .from('employees')
                                .select('name')
                                .eq('code_number', employee.manager_code)
                                .single();
                            
                            return {
                                ...employee,
                                manager: manager ? { name: manager.name } : null
                            };
                        }
                        return {
                            ...employee,
                            manager: null
                        };
                    })
                );
                
                // Populate table
                populateEmployeesTable(employeesWithManagers || []);
                
            } catch (error) {
                console.error('Error loading employees:', error);
                appUtils.showNotification('Error loading employees', 'error');
            }
        }
        
        // Load filter options
        async function loadFilters() {
            try {
                // Load departments
                const { data: departments } = await supabaseClient
                    .from('employees')
                    .select('department')
                    .order('department');
                
                if (departments) {
                    const uniqueDepartments = [...new Set(departments.map(item => item.department))];
                    const departmentFilter = document.getElementById('departmentFilter');
                    
                    uniqueDepartments.forEach(department => {
                        const option = document.createElement('option');
                        option.value = department;
                        option.textContent = department;
                        departmentFilter.appendChild(option);
                    });
                }
                
                // Load positions
                const { data: positions } = await supabaseClient
                    .from('employees')
                    .select('position')
                    .order('position');
                
                if (positions) {
                    const uniquePositions = [...new Set(positions.map(item => item.position))];
                    const positionFilter = document.getElementById('positionFilter');
                    
                    uniquePositions.forEach(position => {
                        const option = document.createElement('option');
                        option.value = position;
                        option.textContent = position;
                        positionFilter.appendChild(option);
                    });
                }
                
                // Load managers
                const { data: managers } = await supabaseClient
                    .from('employees')
                    .select('code_number, name')
                    .eq('is_manager', true)
                    .order('name');
                
                if (managers) {
                    const managerFilter = document.getElementById('managerFilter');
                    const managerSelect = document.getElementById('managerId');
                    
                    managers.forEach(manager => {
                        // For filter
                        const filterOption = document.createElement('option');
                        filterOption.value = manager.code_number;
                        filterOption.textContent = manager.name;
                        managerFilter.appendChild(filterOption);
                        
                        // For form select
                        const formOption = document.createElement('option');
                        formOption.value = manager.code_number;
                        formOption.textContent = manager.name;
                        managerSelect.appendChild(formOption);
                    });
                }
                
            } catch (error) {
                console.error('Error loading filters:', error);
            }
        }
        
        // Populate employees table
        function populateEmployeesTable(employees) {
            const tableBody = document.getElementById('employeesTableBody');
            tableBody.innerHTML = '';
            
            if (employees.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 7;
                cell.textContent = 'No employees found';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.appendChild(row);
                return;
            }
            
            employees.forEach(employee => {
                const row = document.createElement('tr');
                
                // Code
                const codeCell = document.createElement('td');
                codeCell.textContent = employee.code_number;
                row.appendChild(codeCell);
                
                // Name
                const nameCell = document.createElement('td');
                nameCell.textContent = employee.name;
                row.appendChild(nameCell);
                
                // Position
                const positionCell = document.createElement('td');
                positionCell.textContent = employee.position;
                row.appendChild(positionCell);
                
                // Department
                const departmentCell = document.createElement('td');
                departmentCell.textContent = employee.department;
                row.appendChild(departmentCell);
                
                // Manager
                const managerCell = document.createElement('td');
                managerCell.textContent = employee.manager ? employee.manager.name : 'None';
                row.appendChild(managerCell);
                
                // Is Manager
                const isManagerCell = document.createElement('td');
                isManagerCell.innerHTML = employee.is_manager ? 
                    '<span class="badge badge-success">Yes</span>' : 
                    '<span class="badge badge-secondary">No</span>';
                row.appendChild(isManagerCell);
                
                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.innerHTML = `
                    <button class="btn btn-info btn-sm edit-employee" data-id="${employee.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger btn-sm delete-employee" data-id="${employee.id}" data-name="${employee.name}">
                        <i class="fas fa-trash"></i>
                    </button>
                    <a href="assign-kpis.html?code=${employee.code_number}" class="btn btn-primary btn-sm">
                        <i class="fas fa-tasks"></i> KPIs
                    </a>
                `;
                row.appendChild(actionsCell);
                
                tableBody.appendChild(row);
            });
            
            // Add event listeners to edit and delete buttons
            document.querySelectorAll('.edit-employee').forEach(button => {
                button.addEventListener('click', function() {
                    const employeeId = this.getAttribute('data-id');
                    openEmployeeModal(employeeId);
                });
            });
            
            document.querySelectorAll('.delete-employee').forEach(button => {
                button.addEventListener('click', function() {
                    const employeeId = this.getAttribute('data-id');
                    const employeeName = this.getAttribute('data-name');
                    openDeleteModal(employeeId, employeeName);
                });
            });
        }
        
        // Open employee modal
        async function openEmployeeModal(employeeId = null) {
            // Reset form
            document.getElementById('employeeForm').reset();
            document.getElementById('employeeId').value = '';
            document.getElementById('userAccountSection').style.display = 'none';

            // Reset all role checkboxes
            document.getElementById('isAdmin').checked = false;
            document.getElementById('isManager').checked = false;
            document.getElementById('isEmployee').checked = false;
            
            // Set modal title
            const modalTitle = document.getElementById('modalTitle');
            modalTitle.textContent = employeeId ? 'Edit Employee' : 'Add Employee';
            
            if (employeeId) {
                try {
                    // Fetch employee data
                    const { data: employee, error } = await supabaseClient
                        .from('employees')
                        .select('*')
                        .eq('id', employeeId)
                        .single();
                    
                    if (error) throw error;
                    
                    if (employee) {
                        // Populate form
                        document.getElementById('employeeId').value = employee.id;
                        document.getElementById('codeNumber').value = employee.code_number;
                        document.getElementById('name').value = employee.name;
                        document.getElementById('position').value = employee.position;
                        document.getElementById('department').value = employee.department;
                        document.getElementById('managerId').value = employee.manager_code || '';

                        // Check for user account to determine role
                        const { data: user } = await supabaseClient
                            .from('users')
                            .select('username, role')
                            .eq('employee_code', employee.code_number)
                            .single();

                        if (user) {
                            // Set appropriate role checkbox based on user role
                            if (user.role === 'admin') {
                                document.getElementById('isAdmin').checked = true;
                            } else if (user.role === 'manager') {
                                document.getElementById('isManager').checked = true;
                            } else if (user.role === 'employee') {
                                document.getElementById('isEmployee').checked = true;
                            }

                            // Show user account section
                            document.getElementById('userAccountSection').style.display = 'block';
                            document.getElementById('username').value = user.username;

                            // Update required indicators
                            const usernameRequired = document.getElementById('usernameRequired');
                            const passwordRequired = document.getElementById('passwordRequired');
                            if (user.role === 'employee') {
                                usernameRequired.style.display = 'none';
                                passwordRequired.style.display = 'none';
                            } else {
                                usernameRequired.style.display = 'inline';
                                passwordRequired.style.display = 'inline';
                            }
                        } else if (employee.is_manager) {
                            // Legacy support: if no user account but is_manager is true
                            document.getElementById('isManager').checked = true;
                        }
                    }
                } catch (error) {
                    console.error('Error fetching employee:', error);
                    appUtils.showNotification('Error fetching employee data', 'error');
                }
            }
            
            // Show modal
            document.getElementById('employeeModal').style.display = 'block';
        }
        
        // Open delete confirmation modal
        function openDeleteModal(employeeId, employeeName) {
            document.getElementById('deleteEmployeeName').textContent = employeeName;
            document.getElementById('confirmDelete').setAttribute('data-id', employeeId);
            document.getElementById('deleteModal').style.display = 'block';
        }
        
        // Close all modals
        function closeModals() {
            document.getElementById('employeeModal').style.display = 'none';
            document.getElementById('deleteModal').style.display = 'none';
        }
        
        // Save employee
        async function saveEmployee() {
            try {
                // Get form values
                const employeeId = document.getElementById('employeeId').value;
                const codeNumber = document.getElementById('codeNumber').value;
                const name = document.getElementById('name').value;
                const position = document.getElementById('position').value;
                const department = document.getElementById('department').value;
                const managerCode = document.getElementById('managerId').value || null;

                // Get selected role
                const isAdmin = document.getElementById('isAdmin').checked;
                const isManager = document.getElementById('isManager').checked;
                const isEmployee = document.getElementById('isEmployee').checked;

                // Validate required fields
                if (!codeNumber || !name || !position || !department) {
                    appUtils.showNotification('Please fill in all required fields', 'error');
                    return;
                }

                // Validate that only one role is selected
                const selectedRoles = [isAdmin, isManager, isEmployee].filter(Boolean).length;
                if (selectedRoles > 1) {
                    appUtils.showNotification('Please select only one role', 'error');
                    return;
                }

                // Prepare employee data (is_manager for backward compatibility)
                const employeeData = {
                    code_number: codeNumber,
                    name: name,
                    position: position,
                    department: department,
                    manager_code: managerCode,
                    is_manager: isManager || isAdmin  // Admin is also considered manager for backward compatibility
                };
                
                let result;
                
                if (employeeId) {
                    // Update existing employee
                    result = await supabaseClient
                        .from('employees')
                        .update(employeeData)
                        .eq('id', employeeId);
                } else {
                    // Create new employee
                    result = await supabaseClient
                        .from('employees')
                        .insert(employeeData)
                        .select();
                }
                
                if (result.error) throw result.error;
                
                // Handle user account creation/update
                if (isAdmin || isManager || isEmployee) {
                    const username = document.getElementById('username').value;
                    const password = document.getElementById('password').value;

                    // Determine user role
                    let userRole = '';
                    if (isAdmin) userRole = 'admin';
                    else if (isManager) userRole = 'manager';
                    else if (isEmployee) userRole = 'employee';

                    // Validate required fields for admin and manager
                    if ((isAdmin || isManager) && !username) {
                        appUtils.showNotification('Username is required for admin and manager accounts', 'error');
                        return;
                    }

                    // Handle user account if username is provided
                    if (username) {
                        // Check if user account already exists for this employee
                        const { data: existingUser } = await supabaseClient
                            .from('users')
                            .select('id')
                            .eq('employee_code', codeNumber)
                            .single();

                        if (existingUser) {
                            // Update existing user
                            const userData = {
                                username: username,
                                role: userRole
                            };

                            // Only update password if provided
                            if (password) {
                                userData.password = password;
                            }

                            await supabaseClient
                                .from('users')
                                .update(userData)
                                .eq('id', existingUser.id);
                        } else {
                            // Create new user
                            if ((isAdmin || isManager) && !password) {
                                appUtils.showNotification('Password is required for new admin and manager accounts', 'error');
                                return;
                            }

                            // Create user directly linked to employee
                            const { error: userError } = await supabaseClient
                                .from('users')
                                .insert({
                                    username: username,
                                    password: password || 'defaultpass123', // Default password for employees if not provided
                                    role: userRole,
                                    employee_code: codeNumber
                                });

                            if (userError) throw userError;
                        }
                    }
                } else {
                    // If no role selected, remove any existing user account
                    const { data: existingUser } = await supabaseClient
                        .from('users')
                        .select('id')
                        .eq('employee_code', codeNumber)
                        .single();

                    if (existingUser) {
                        await supabaseClient
                            .from('users')
                            .delete()
                            .eq('id', existingUser.id);
                    }
                }
                
                // Show success message
                appUtils.showNotification(
                    employeeId ? 'Employee updated successfully' : 'Employee added successfully',
                    'success'
                );
                
                // Close modal and reload employees
                closeModals();
                loadEmployees();
                
            } catch (error) {
                console.error('Error saving employee:', error);
                appUtils.showNotification('Error saving employee', 'error');
            }
        }
        
        // Delete employee
        async function deleteEmployee() {
            try {
                const employeeId = document.getElementById('confirmDelete').getAttribute('data-id');
                
                // Delete employee
                const { error } = await supabaseClient
                    .from('employees')
                    .delete()
                    .eq('id', employeeId);
                
                if (error) throw error;
                
                // Show success message
                appUtils.showNotification('Employee deleted successfully', 'success');
                
                // Close modal and reload employees
                closeModals();
                loadEmployees();
                
            } catch (error) {
                console.error('Error deleting employee:', error);
                appUtils.showNotification('Error deleting employee', 'error');
            }
        }
    </script>
    
    <style>
        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }
        
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-dialog {
            position: relative;
            width: 100%;
            max-width: 500px;
            margin: 1.75rem auto;
        }
        
        .modal-content {
            position: relative;
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--card-border);
        }
        
        .modal-body {
            padding: 1rem;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            padding: 1rem;
            border-top: 1px solid var(--card-border);
        }
        
        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--secondary-color);
        }
        
        /* Form check styles */
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 0;
        }
        
        .form-check-input {
            margin-right: 0.5rem;
        }
    </style>

    <script>
        // Load current user name and show welcome message
        async function loadCurrentUserName() {
            try {
                const currentUser = appAuth.getCurrentUser();
                if (currentUser) {
                    // Update display name to show only first name or "Master Admin"
                    appUtils.updateUserDisplayName('currentUserName', {
                        role: 'admin',
                        name: currentUser.username || currentUser.name
                    });

                    // Show welcome message
                    appUtils.showWelcomeMessage({
                        role: 'admin',
                        name: currentUser.username || currentUser.name
                    });
                } else {
                    document.getElementById('currentUserName').textContent = 'Admin';
                }
            } catch (error) {
                console.error('Error loading user name:', error);
                document.getElementById('currentUserName').textContent = 'Admin';
            }
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });
        });
    </script>
</body>
</html>