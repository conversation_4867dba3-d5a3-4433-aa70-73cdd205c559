# RLS Frontend Integration Guide

## Important: Frontend Code Changes Required

After running the RLS policies, you need to modify your frontend authentication to set the user context for R<PERSON> to work properly.

### 1. After User Login (in your login.php or authentication handler)

Add this SQL command after successful authentication:

```php
// After verifying username/password, set the user context
$username = $_POST['username']; // or however you get the username

// Execute this SQL to set the current user context
$sql = "SET app.current_user = ?";
$stmt = $pdo->prepare($sql);
$stmt->execute([$username]);
```

### 2. Alternative: Set in Database Connection

You can also set this when establishing the database connection:

```php
// In your database connection file
function setUserContext($pdo, $username) {
    $sql = "SET app.current_user = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$username]);
}

// Call this after login
setUserContext($pdo, $logged_in_username);
```

### 3. Session Management

Make sure to set the user context on every page load for logged-in users:

```php
// At the top of each protected page
if (isset($_SESSION['username'])) {
    setUserContext($pdo, $_SESSION['username']);
}
```

## Testing the RLS Implementation

### Test Cases to Verify:

1. **Admin User**: Should have full access to all data
2. **Manager User**: Should only see their team hierarchy data
3. **Employee User**: Should only see their own data

### Quick Test Queries:

```sql
-- Test as admin
SET app.current_user = 'admin';
SELECT * FROM employees; -- Should return all employees

-- Test as manager
SET app.current_user = 'parent_manager';
SELECT * FROM employees; -- Should return only team hierarchy

-- Test as employee
SET app.current_user = 'ahmed_atef';
SELECT * FROM employees; -- Should return only own record
```

## Rollback Instructions

If RLS causes issues, you can quickly disable it:

```sql
ALTER TABLE employees DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE kpi_categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE kpis DISABLE ROW LEVEL SECURITY;
ALTER TABLE employee_kpis DISABLE ROW LEVEL SECURITY;
ALTER TABLE category_weights DISABLE ROW LEVEL SECURITY;
ALTER TABLE appraisal_periods DISABLE ROW LEVEL SECURITY;
ALTER TABLE appraisals DISABLE ROW LEVEL SECURITY;
ALTER TABLE appraisal_scores DISABLE ROW LEVEL SECURITY;
ALTER TABLE appraisal_assignments DISABLE ROW LEVEL SECURITY;
```

This will restore your system to the current state without any data loss.
