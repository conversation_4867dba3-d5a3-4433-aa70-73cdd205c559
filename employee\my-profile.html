<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - HR Performance Evaluation System</title>
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="icon" type="image/png" sizes="96x96" href="../assets/icons/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" sizes="any" href="../assets/icons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="../assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="../assets/icons/web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../assets/icons/web-app-manifest-512x512.png">
    <link rel="manifest" href="../manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="msapplication-config" content="../assets/icons/browserconfig.xml">
    

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <style>
        /* Profile specific styling */
        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .profile-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
        }

        .info-item {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-5px);
        }

        .info-label {
            font-weight: 700;
            color: #6c757d;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .info-value {
            font-size: 1.4rem;
            color: #2c3e50;
            font-weight: 600;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .score-display {
            text-align: center;
            padding: 2rem;
            max-width: 400px;
            margin: 0 auto;
        }

        .score-circle {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 0 15px 35px rgba(40, 167, 69, 0.3);
            position: relative;
        }

        .score-circle::before {
            content: '';
            position: absolute;
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            top: 10px;
            left: 10px;
        }

        .score-text {
            color: white;
            font-size: 2.8rem;
            font-weight: 900;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            z-index: 1;
        }

        .score-label {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .no-score {
            color: #6c757d;
            font-style: italic;
            font-size: 1.1rem;
        }

        /* Navigation button press effects */
        .nav-link {
            transition: all 0.3s ease;
            transform: scale(1);
        }

        .nav-link.pressed {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }

        .nav-link.active {
            transform: scale(0.9);
            background-color: #0056b3 !important;
            color: white !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                    <div class="user-tabs">
                        <button class="tab-button" id="settingsBtn" title="Settings">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="tab-button" id="notificationsBtn" title="Notifications">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                        </button>
                    </div>
                </div>
            </div>
        <nav class="nav-bottom">
            <a href="my-profile.html" class="nav-link active">
                <i class="fas fa-user"></i> My Profile
            </a>
            <a href="my-appraisal.html" class="nav-link">
                <i class="fas fa-chart-bar"></i> My Appraisal
            </a>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <h1 class="page-title">My Profile</h1>

            <!-- Employee Information Section -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="text-center mb-4">
                        <h2 class="section-title">
                            <i class="fas fa-user-circle text-primary me-3"></i>
                            Employee Information
                        </h2>
                    </div>
                    <div class="profile-info" id="profileInfo">
                        <!-- Profile information will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Last Appraisal Score Section -->
            <div class="row">
                <div class="col-12">
                    <div class="text-center mb-4">
                        <h2 class="section-title">
                            <i class="fas fa-star text-warning me-3"></i>
                            Last Performance Score
                        </h2>
                    </div>
                    <div class="score-display" id="scoreDisplay">
                        <!-- Score will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/supabase.js"></script>
    <script src="../assets/js/utils.js"></script>

    <script>
        let currentEmployee = null;

        document.addEventListener('DOMContentLoaded', async function() {
            // Check authentication
            if (!appAuth.checkAuth('employee')) {
                return;
            }

            // User name will be loaded with employee data
            
            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();

            // Logout functionality
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });

            // Load profile data
            await loadProfileData();
        });

        // Load profile data
        async function loadProfileData() {
            try {
                console.log('Employee Profile: Loading profile data...');
                
                // Get current employee
                currentEmployee = appAuth.getCurrentEmployee();
                console.log('Employee Profile: Current employee:', currentEmployee);
                if (!currentEmployee) {
                    appUtils.showNotification('Employee information not found', 'error');
                    return;
                }

                // Update user name display to show only first name
                appUtils.updateUserDisplayName('currentUserName', {
                    role: 'employee',
                    name: currentEmployee.name
                });

                // Show welcome message
                appUtils.showWelcomeMessage({
                    role: 'employee',
                    name: currentEmployee.name
                });

                // Get manager information
                let managerName = 'N/A';
                if (currentEmployee.manager_code) {
                    const { data: manager } = await supabaseClient
                        .from('employees')
                        .select('name')
                        .eq('code_number', currentEmployee.manager_code)
                        .single();

                    if (manager) {
                        managerName = manager.name;
                    }
                }

                // Determine employee level (Manager or Non-manager)
                let employeeLevel = 'Non-manager';
                const { data: directReports } = await supabaseClient
                    .from('employees')
                    .select('code_number')
                    .eq('manager_code', currentEmployee.code_number);

                if (directReports && directReports.length > 0) {
                    employeeLevel = 'Manager';
                }

                // Display profile information
                displayProfileInfo(currentEmployee, managerName, employeeLevel);

                // Load last appraisal score
                await loadLastAppraisalScore();

            } catch (error) {
                console.error('Error loading profile data:', error);
                appUtils.showNotification('Error loading profile data', 'error');
            }
        }

        // Display profile information
        function displayProfileInfo(employee, managerName, employeeLevel) {
            const profileInfo = document.getElementById('profileInfo');

            profileInfo.innerHTML = `
                <div class="info-item">
                    <div class="info-label">Full Name</div>
                    <div class="info-value">${employee.name}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Employee Code</div>
                    <div class="info-value">${employee.code_number}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Position</div>
                    <div class="info-value">${employee.position}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Department</div>
                    <div class="info-value">${employee.department}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Level</div>
                    <div class="info-value">
                        <span class="badge ${employeeLevel === 'Manager' ? 'bg-primary' : 'bg-secondary'}">
                            ${employeeLevel}
                        </span>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">Direct Manager</div>
                    <div class="info-value">${managerName}</div>
                </div>
            `;
        }

        // Load last appraisal score
        async function loadLastAppraisalScore() {
            try {
                console.log('Employee Profile: Loading last appraisal score...');
                console.log('Employee Profile: Current employee code:', currentEmployee.code_number);

                const { data: lastAppraisal, error } = await supabaseClient
                    .from('appraisals')
                    .select('total_score, created_at')
                    .eq('employee_code', currentEmployee.code_number)
                    .order('created_at', { ascending: false })
                    .limit(1);

                if (error && error.code !== 'PGRST116') {
                    throw error;
                }

                const scoreDisplay = document.getElementById('scoreDisplay');
                
                if (lastAppraisal && lastAppraisal.length > 0) {
                    const score = parseFloat(lastAppraisal[0].total_score) || 0;
                    const date = new Date(lastAppraisal[0].created_at).toLocaleDateString();
                    
                    scoreDisplay.innerHTML = `
                        <div class="score-circle">
                            <div class="score-text">${score.toFixed(1)}%</div>
                        </div>
                        <div class="score-label">Total Score</div>
                        <p class="text-muted mt-2">Last evaluated on ${date}</p>
                    `;
                } else {
                    scoreDisplay.innerHTML = `
                        <div class="score-circle">
                            <div class="score-text">N/A</div>
                        </div>
                        <div class="score-label">No Appraisal Yet</div>
                        <p class="text-muted mt-2">You haven't been evaluated yet</p>
                    `;
                }

            } catch (error) {
                console.error('Error loading last appraisal score:', error);
                document.getElementById('scoreDisplay').innerHTML = `
                    <div class="score-circle">
                        <div class="score-text">Error</div>
                    </div>
                    <div class="score-label">Unable to load score</div>
                `;
            }
        }

        // Navigation button press effects
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove pressed class from all nav links
                    navLinks.forEach(nav => nav.classList.remove('pressed'));

                    // Add pressed class to clicked link
                    this.classList.add('pressed');
                });
            });

            // Initialize Settings and Notifications
            initializeSettingsAndNotifications();
        });

        // Global function for password visibility toggle
        function togglePasswordVisibility(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + 'Icon');

            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }
    </script>

    <!-- Settings and Notifications -->
    <script src="../assets/js/settings-notifications.js"></script>

    <!-- Settings Modal -->
    <div class="settings-modal" id="settingsModal">
        <div class="settings-content">
            <div class="modal-header">
                <h5 class="modal-title">Settings</h5>
                <button class="close-btn" onclick="closeSettingsModal()">&times;</button>
            </div>
            <div class="modal-body">
                <h6 class="mb-3">Change Password</h6>
                <form id="changePasswordForm">
                    <div class="form-group mb-3">
                        <label for="currentPassword" class="form-label">Current Password</label>
                        <div class="input-group">
                            <input type="password" id="currentPassword" class="form-control" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('currentPassword')">
                                <i class="fas fa-eye" id="currentPasswordIcon"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="newPassword" class="form-label">New Password</label>
                        <div class="input-group">
                            <input type="password" id="newPassword" class="form-control" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('newPassword')">
                                <i class="fas fa-eye" id="newPasswordIcon"></i>
                            </button>
                        </div>
                        <small class="text-muted">4-12 characters, letters, numbers, and must contain _ or @</small>
                    </div>
                    <div class="form-group mb-3">
                        <label for="confirmPassword" class="form-label">Confirm New Password</label>
                        <div class="input-group">
                            <input type="password" id="confirmPassword" class="form-control" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('confirmPassword')">
                                <i class="fas fa-eye" id="confirmPasswordIcon"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">Save Password</button>
                        <button type="button" class="btn btn-secondary" onclick="closeSettingsModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Notifications Modal -->
    <div class="notifications-modal" id="notificationsModal">
        <div class="notifications-content">
            <div class="modal-header">
                <h5 class="modal-title">Notifications</h5>
                <button class="close-btn" onclick="closeNotificationsModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="notificationsList">
                    <div class="empty-notifications">
                        <i class="fas fa-bell-slash fa-2x mb-2"></i>
                        <p>No notifications</p>
                    </div>
                </div>
                <div class="notification-actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">Mark All as Read</button>
                    <button class="btn btn-outline-danger btn-sm" onclick="clearAllNotifications()">Clear All</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
