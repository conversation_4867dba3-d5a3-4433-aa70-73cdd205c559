<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign KPIs - HR Performance Evaluation System</title>
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <link rel="icon" type="image/png" sizes="96x96" href="../assets/icons/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" sizes="any" href="../assets/icons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="../assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="../assets/icons/web-app-manifest-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../assets/icons/web-app-manifest-512x512.png">
    <link rel="manifest" href="../manifest.json">
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="msapplication-config" content="../assets/icons/browserconfig.xml">
    
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Select CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/styles.css">

    <style>
        /* Modern card styling */
        .card {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
        }

        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            border: none;
            padding: 10px 15px;
        }

        .card-body {
            padding: 10px;
        }

        /* Dark mode support */
        body.dark-mode .card {
            background-color: #1e1e1e;
            border-color: #333333;
            color: #e0e0e0;
        }

        body.dark-mode .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            border-color: #333333;
            color: white;
        }

        body.dark-mode .table {
            color: #e0e0e0;
        }

        body.dark-mode .table thead th {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }

        body.dark-mode .form-label {
            color: #e0e0e0;
        }

        body.dark-mode h1, body.dark-mode h2, body.dark-mode h3, body.dark-mode h5 {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-top">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <span class="logo-text">Performance Evaluation System</span>
                </div>
                <div class="user-info">
                    <span id="currentUserName" class="user-name">Loading...</span>
                    <a href="#" id="logoutBtn" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
            <nav class="nav-bottom">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="employees.html" class="nav-link">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a href="kpis.html" class="nav-link">
                    <i class="fas fa-bullseye"></i> KPIs
                </a>
                <a href="assign-kpis.html" class="nav-link active">
                    <i class="fas fa-user-tag"></i> Assign KPIs
                </a>
                <a href="assign-appraisals.html" class="nav-link">
                    <i class="fas fa-clipboard-list"></i> Assign Appraisals
                </a>
                <a href="reports.html" class="nav-link">
                    <i class="fas fa-chart-bar"></i> Reports
                </a>
            </nav>
        </div>
    </header>
    
    <!-- Main content -->
    <main class="main">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-2xl font-bold">Assign KPIs to Employee</h1>
                <a href="employees.html" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Employees
                </a>
            </div>
            
            <!-- Employee Info -->
            <div class="card mb-4">
                <div class="card-header py-2">
                    <h5 class="mb-0">Employee Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Name:</strong> <span id="employeeName"></span></p>
                            <p><strong>Code:</strong> <span id="employeeCode"></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Position:</strong> <span id="employeePosition"></span></p>
                            <p><strong>Department:</strong> <span id="employeeDepartment"></span></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Category Weights -->
            <div class="card mb-4">
                <div class="card-header py-2">
                    <h5 class="mb-0">Category Weights</h5>
                </div>
                <div class="card-body">
                    <p class="mb-3">Set the weight percentage for each KPI category. Total must equal 100%.</p>
                    
                    <div id="categoryWeightsContainer">
                        <!-- Category weights will be loaded dynamically -->
                    </div>
                    
                    <div class="mt-3">
                        <button id="saveCategoryWeights" class="btn btn-primary">Save Category Weights</button>
                    </div>
                </div>
            </div>
            
            <!-- Assigned KPIs -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center py-2">
                    <h5 class="mb-0">Assigned KPIs</h5>
                    <div>
                        <button id="validateAllBtn" class="btn btn-light btn-sm me-2">
                            <i class="fas fa-check me-1"></i> Validate KPI Weights
                        </button>
                        <button id="addKpiBtn" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus mr-1"></i> Add KPI
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="assignedKpisTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>KPI</th>
                                    <th>Category</th>
                                    <th>Weight (%)</th>
                                    <th>Measurement</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="assignedKpisTableBody">
                                <!-- Assigned KPIs will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Add KPI Modal -->
    <div id="kpiModal" class="modal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Assign KPI</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="assignKpiForm">
                        <input type="hidden" id="employeeKpiId">
                        
                        <div class="form-group">
                            <label for="kpiCategory" class="form-label">Category</label>
                            <select id="kpiCategory" class="form-select" required>
                                <option value="">Select Category</option>
                                <!-- Categories will be loaded dynamically -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="kpiSelect" class="form-label">KPI</label>
                            <select id="kpiSelect" class="form-control selectpicker" required data-live-search="true" data-size="5" title="Type to search KPIs...">
                                <option value="">Select KPI</option>
                                <!-- KPIs will be loaded dynamically based on selected category -->
                            </select>
                            <small class="text-muted">Type to search KPI names</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="kpiWeight" class="form-label">Weight (%)</label>
                            <input type="number" id="kpiWeight" class="form-control" min="1" max="100" required>
                            <div id="weightInfo" class="mt-2">
                                <small class="text-muted">Current category total: <span id="currentCategoryTotal">0</span>%</small><br>
                                <small class="text-muted">Remaining: <span id="remainingWeight">100</span>%</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="kpiMeasurement" class="form-label">Custom Measurement Description</label>
                            <textarea id="kpiMeasurement" class="form-control" rows="5" placeholder="Enter specific measurement criteria for this employee (optional). Press Enter for new lines."></textarea>
                            <small class="text-muted">This will be shown below the KPI description in the appraisal form</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="button" id="saveKpi" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirm Delete</h3>
                    <button type="button" class="close-modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to remove this KPI from the employee?</p>
                    <p id="deleteKpiName" class="font-bold"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                    <button type="button" id="confirmDelete" class="btn btn-danger">Delete</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="footer-text">© Produced by: Dr. Ahmed Atef - All rights reserved.</p>
        </div>
    </footer>
    
    <!-- Dark mode toggle -->
    <div id="darkModeToggle" class="dark-mode-toggle" title="Switch to Dark Mode">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- jQuery (required for Bootstrap Select) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Bootstrap Select JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>

    <!-- Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/utils.js"></script>
    <script src="../assets/js/supabase.js"></script>
    
    <script>
        // Get employee code from URL parameter
        function getEmployeeCodeFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('code');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and role
            if (!appAuth.checkAuth('admin')) {
                return;
            }

            // Load current user name
            loadCurrentUserName();

            // Initialize enhanced navigation
            appUtils.initializeNavigation();

            // Apply dark mode if previously enabled
            appUtils.applyDarkMode();
            
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', function() {
                appUtils.toggleDarkMode();
            });
            
            // Logout button
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                appAuth.logoutUser();
            });
            
            // Get employee code from URL
            const params = appUtils.getUrlParams();
            const employeeCode = params.code;
            
            if (!employeeCode) {
                window.location.href = 'employees.html';
                return;
            }
            
            // Load employee data
            loadEmployeeData(employeeCode);
            
            // Load categories
            loadCategories();
            
            // Load assigned KPIs
            loadAssignedKPIs(employeeCode);

            // Initialize Bootstrap Select when page loads with enhanced options
            setTimeout(() => {
                try {
                    if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                        $('.selectpicker').selectpicker({
                            liveSearch: true,
                            size: 8,
                            title: 'Type to search KPIs...',
                            showTick: true,
                            dropupAuto: false,
                            actionsBox: false,
                            noneResultsText: 'No KPIs match your search',
                            style: 'btn-outline-secondary',
                            styleBase: 'form-control',
                            liveSearchPlaceholder: 'Search KPIs...',
                            liveSearchNormalize: true,
                            liveSearchStyle: 'contains'
                        });

                        // Add enhanced event handlers for better selection
                        $('#kpiSelect').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
                            const selectedValue = $(this).val();
                            const selectedText = $(this).find('option:selected').text();
                            console.log('KPI Selection Changed:', { value: selectedValue, text: selectedText });
                        });

                        // Auto-focus search box when dropdown opens
                        $('#kpiSelect').on('show.bs.select', function () {
                            setTimeout(() => {
                                $('.bs-searchbox input').focus();
                            }, 100);
                        });

                    } else {
                        console.warn('Bootstrap Select not available, using standard dropdown');
                    }
                } catch (error) {
                    console.error('Error initializing Bootstrap Select on page load:', error);
                }
            }, 200);

            // Category dropdown change event
            document.getElementById('kpiCategory').addEventListener('change', function() {
                loadKPIsByCategory(this.value);
            });

            // Weight input change event
            document.getElementById('kpiWeight').addEventListener('input', function() {
                const categoryId = document.getElementById('kpiCategory').value;
                if (categoryId) {
                    updateWeightInfo(categoryId, null);
                }
            });
            
            // Add KPI button
            document.getElementById('addKpiBtn').addEventListener('click', function() {
                openKpiModal();
            });

            // Validate All button
            document.getElementById('validateAllBtn').addEventListener('click', function() {
                validateAllCategories();
            });
            
            // Save KPI button
            document.getElementById('saveKpi').addEventListener('click', function() {
                assignKPI(employeeCode);
            });
            
            // Save Category Weights button
            document.getElementById('saveCategoryWeights').addEventListener('click', function() {
                saveCategoryWeights(employeeCode);
            });
            
            // Confirm Delete button
            document.getElementById('confirmDelete').addEventListener('click', function() {
                const kpiId = this.getAttribute('data-id');
                removeKPI(kpiId, employeeCode);
            });
            
            // Close modal buttons
            document.querySelectorAll('.close-modal').forEach(button => {
                button.addEventListener('click', function() {
                    closeModals();
                });
            });
        });
        
        // Load employee data
        async function loadEmployeeData(employeeCode) {
            try {
                const { data: employee, error } = await supabaseClient
                    .from('employees')
                    .select('*')
                    .eq('code_number', employeeCode)
                    .single();
                
                if (error) throw error;
                
                if (employee) {
                    document.getElementById('employeeName').textContent = employee.name;
                    document.getElementById('employeeCode').textContent = employee.code_number;
                    document.getElementById('employeePosition').textContent = employee.position;
                    document.getElementById('employeeDepartment').textContent = employee.department;
                }
                
            } catch (error) {
                console.error('Error loading employee data:', error);
                appUtils.showNotification('Error loading employee data', 'error');
            }
        }
        
        // Load categories
        async function loadCategories() {
            try {
                const { data: categories, error } = await supabaseClient
                    .from('kpi_categories')
                    .select('*')
                    .order('name');
                
                if (error) throw error;
                
                // Populate category dropdown
                const categorySelect = document.getElementById('kpiCategory');
                categorySelect.innerHTML = '<option value="">Select Category</option>';
                
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
                
                // Create category weights form
                createCategoryWeightsForm(categories);
                
            } catch (error) {
                console.error('Error loading categories:', error);
                appUtils.showNotification('Error loading categories', 'error');
            }
        }
        
        // Create category weights form
        async function createCategoryWeightsForm(categories) {
            try {
                const employeeCode = appUtils.getUrlParams().code;
                
                // Get existing weights
                const { data: existingWeights, error } = await supabaseClient
                    .from('category_weights')
                    .select('*')
                    .eq('employee_code', employeeCode);
                
                if (error) throw error;
                
                // Create form elements
                const container = document.getElementById('categoryWeightsContainer');
                container.innerHTML = '';
                
                categories.forEach(category => {
                    // Find existing weight for this category
                    const existingWeight = existingWeights?.find(w => w.category_id === category.id);
                    const weightValue = existingWeight ? existingWeight.weight : 0;
                    
                    const formGroup = document.createElement('div');
                    formGroup.className = 'form-group mb-3';
                    
                    const label = document.createElement('label');
                    label.className = 'form-label';
                    label.textContent = category.name;
                    
                    const input = document.createElement('input');
                    input.type = 'number';
                    input.className = 'form-control category-weight';
                    input.min = 0;
                    input.max = 100;
                    input.value = weightValue;
                    input.dataset.categoryId = category.id;
                    
                    formGroup.appendChild(label);
                    formGroup.appendChild(input);
                    container.appendChild(formGroup);
                });
                
                // Add total display
                const totalGroup = document.createElement('div');
                totalGroup.className = 'form-group mt-4';
                
                const totalLabel = document.createElement('label');
                totalLabel.className = 'form-label font-bold';
                totalLabel.textContent = 'Total:';
                
                const totalSpan = document.createElement('span');
                totalSpan.id = 'totalWeight';
                totalSpan.className = 'ml-2';
                totalSpan.textContent = '0%';
                
                totalGroup.appendChild(totalLabel);
                totalGroup.appendChild(totalSpan);
                container.appendChild(totalGroup);
                
                // Add event listeners to update total
                const weightInputs = document.querySelectorAll('.category-weight');
                weightInputs.forEach(input => {
                    input.addEventListener('input', updateTotalWeight);
                });
                
                // Initial calculation
                updateTotalWeight();
                
            } catch (error) {
                console.error('Error creating category weights form:', error);
                appUtils.showNotification('Error loading category weights', 'error');
            }
        }
        
        // Update total weight
        function updateTotalWeight() {
            const weightInputs = document.querySelectorAll('.category-weight');
            let total = 0;
            
            weightInputs.forEach(input => {
                total += parseInt(input.value) || 0;
            });
            
            const totalElement = document.getElementById('totalWeight');
            totalElement.textContent = total + '%';
            
            // Highlight if not 100%
            if (total !== 100) {
                totalElement.className = 'ml-2 text-danger';
            } else {
                totalElement.className = 'ml-2 text-success';
            }
        }
        
        // Save category weights
        async function saveCategoryWeights(employeeCode) {
            try {
                const weightInputs = document.querySelectorAll('.category-weight');
                let total = 0;
                
                weightInputs.forEach(input => {
                    total += parseInt(input.value) || 0;
                });
                
                if (total !== 100) {
                    appUtils.showNotification('Total weight must equal 100%', 'error');
                    return;
                }
                
                // Delete existing weights
                const { error: deleteError } = await supabaseClient
                    .from('category_weights')
                    .delete()
                    .eq('employee_code', employeeCode);
                
                if (deleteError) throw deleteError;
                
                // Insert new weights
                const weights = [];
                
                weightInputs.forEach(input => {
                    const categoryId = input.dataset.categoryId;
                    const weight = parseInt(input.value) || 0;
                    
                    if (weight > 0) {
                        weights.push({
                            employee_code: employeeCode,
                            category_id: categoryId,
                            weight: weight
                        });
                    }
                });
                
                const { error: insertError } = await supabaseClient
                    .from('category_weights')
                    .insert(weights);
                
                if (insertError) throw insertError;
                
                appUtils.showNotification('Category weights saved successfully', 'success');
                
            } catch (error) {
                console.error('Error saving category weights:', error);
                appUtils.showNotification('Error saving category weights', 'error');
            }
        }
        
        // Load KPIs by category
        async function loadKPIsByCategory(categoryId) {
            try {
                if (!categoryId) {
                    document.getElementById('kpiSelect').innerHTML = '<option value="">Select KPI</option>';
                    updateWeightInfo(null, null);
                    return;
                }

                const { data: kpis, error } = await supabaseClient
                    .from('kpis')
                    .select('*')
                    .eq('category_id', categoryId);

                if (error) throw error;

                // Populate KPI dropdown
                const kpiSelect = document.getElementById('kpiSelect');
                kpiSelect.innerHTML = '<option value="">Select KPI</option>';

                kpis.forEach(kpi => {
                    const option = document.createElement('option');
                    option.value = kpi.id;
                    option.textContent = kpi.name;
                    kpiSelect.appendChild(option);
                });

                // Refresh Bootstrap Select after updating options
                try {
                    if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                        $('#kpiSelect').selectpicker('refresh');
                    }
                } catch (error) {
                    console.error('Error refreshing Bootstrap Select:', error);
                }

                // Update weight information for this category
                await updateWeightInfo(categoryId, null);

            } catch (error) {
                console.error('Error loading KPIs:', error);
                appUtils.showNotification('Error loading KPIs', 'error');
            }
        }

        // Update weight information display
        async function updateWeightInfo(categoryId, excludeEmployeeKpiId) {
            try {
                if (!categoryId) {
                    document.getElementById('currentCategoryTotal').textContent = '0';
                    document.getElementById('remainingWeight').textContent = '100';
                    return;
                }

                const employeeCode = getEmployeeCodeFromURL();

                // Get all KPIs for this employee, then filter by category (same approach as validation)
                const { data: allKpis, error } = await supabaseClient
                    .from('employee_kpis')
                    .select(`
                        id,
                        weight,
                        kpis (
                            category_id
                        )
                    `)
                    .eq('employee_code', employeeCode);

                if (error) throw error;

                // Filter by category in JavaScript
                const currentKpis = allKpis.filter(kpi =>
                    kpi.kpis && kpi.kpis.category_id === categoryId
                );

                let totalWeight = 0;
                currentKpis.forEach(kpi => {
                    // Exclude the KPI being edited (by employee_kpi ID, not KPI ID)
                    if (!excludeEmployeeKpiId || kpi.id !== excludeEmployeeKpiId) {
                        totalWeight += parseFloat(kpi.weight);
                    }
                });

                const remaining = 100 - totalWeight;

                document.getElementById('currentCategoryTotal').textContent = totalWeight.toFixed(0);
                document.getElementById('remainingWeight').textContent = remaining.toFixed(0);

                // Update remaining weight color
                const remainingElement = document.getElementById('remainingWeight');
                if (remaining < 0) {
                    remainingElement.style.color = 'var(--danger-color)';
                } else if (remaining === 0) {
                    remainingElement.style.color = 'var(--success-color)';
                } else {
                    remainingElement.style.color = 'var(--warning-color)';
                }

            } catch (error) {
                console.error('Error updating weight info:', error);
                appUtils.showNotification('Error loading KPI weight information', 'error');
            }
        }
        
        // Load assigned KPIs
        async function loadAssignedKPIs(employeeCode) {
            try {
                const { data: assignedKpis, error } = await supabaseClient
                    .from('employee_kpis')
                    .select(`
                        id,
                        weight,
                        measurement,
                        kpi:kpis(id, name, category_id),
                        category:kpis(kpi_categories(id, name))
                    `)
                    .eq('employee_code', employeeCode)
                    .order('created_at');
                
                if (error) throw error;
                
                // Populate table
                const tableBody = document.getElementById('assignedKpisTableBody');
                tableBody.innerHTML = '';
                
                if (!assignedKpis || assignedKpis.length === 0) {
                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.colSpan = 5; // Updated for new measurement column
                    cell.className = 'text-center';
                    cell.textContent = 'No KPIs assigned yet';
                    row.appendChild(cell);
                    tableBody.appendChild(row);
                    return;
                }
                
                assignedKpis.forEach(item => {
                    const row = document.createElement('tr');
                    
                    // KPI Name
                    const nameCell = document.createElement('td');
                    nameCell.textContent = item.kpi.name;
                    row.appendChild(nameCell);
                    
                    // Category
                    const categoryCell = document.createElement('td');
                    categoryCell.textContent = item.category.kpi_categories.name;
                    row.appendChild(categoryCell);
                    
                    // Weight
                    const weightCell = document.createElement('td');
                    weightCell.textContent = item.weight + '%';
                    row.appendChild(weightCell);

                    // Measurement
                    const measurementCell = document.createElement('td');
                    measurementCell.textContent = item.measurement || 'No custom measurement';
                    measurementCell.className = 'text-muted';
                    if (item.measurement) {
                        measurementCell.className = '';
                        measurementCell.style.maxWidth = '200px';
                        measurementCell.style.overflow = 'hidden';
                        measurementCell.style.textOverflow = 'ellipsis';
                        measurementCell.style.whiteSpace = 'nowrap';
                        measurementCell.title = item.measurement; // Show full text on hover
                    }
                    row.appendChild(measurementCell);

                    // Actions
                    const actionsCell = document.createElement('td');
                    
                    const editBtn = document.createElement('button');
                    editBtn.className = 'btn btn-sm btn-primary mr-1';
                    editBtn.innerHTML = '<i class="fas fa-edit"></i>';
                    editBtn.title = 'Edit';
                    editBtn.addEventListener('click', () => editKPI(item));
                    
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'btn btn-sm btn-danger';
                    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                    deleteBtn.title = 'Delete';
                    deleteBtn.addEventListener('click', () => confirmDeleteKPI(item));
                    
                    actionsCell.appendChild(editBtn);
                    actionsCell.appendChild(deleteBtn);
                    row.appendChild(actionsCell);
                    
                    tableBody.appendChild(row);
                });

                // Update weight info display - reset to default since no category is selected
                document.getElementById('currentCategoryTotal').textContent = '0';
                document.getElementById('remainingWeight').textContent = '100';

            } catch (error) {
                console.error('Error loading assigned KPIs:', error);
                appUtils.showNotification('Error loading assigned KPIs', 'error');
            }
        }
        
        // Open KPI modal
        function openKpiModal() {
            document.getElementById('employeeKpiId').value = '';
            document.getElementById('kpiCategory').value = '';
            document.getElementById('kpiSelect').innerHTML = '<option value="">Select KPI</option>';
            document.getElementById('kpiWeight').value = '';
            document.getElementById('kpiMeasurement').value = '';

            // Initialize Bootstrap Select for searchable dropdown with enhanced options
            try {
                if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                    // Destroy existing selectpicker if it exists
                    $('#kpiSelect').selectpicker('destroy');

                    // Reinitialize with enhanced options
                    $('#kpiSelect').selectpicker({
                        liveSearch: true,
                        size: 8,
                        title: 'Type to search KPIs...',
                        showTick: true,
                        dropupAuto: false,
                        actionsBox: false,
                        noneResultsText: 'No KPIs match your search',
                        style: 'btn-outline-secondary',
                        styleBase: 'form-control',
                        liveSearchPlaceholder: 'Search KPIs...',
                        liveSearchNormalize: true,
                        liveSearchStyle: 'contains'
                    });

                    // Add event handlers for better interaction
                    $('#kpiSelect').off('changed.bs.select').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {
                        const selectedValue = $(this).val();
                        const selectedText = $(this).find('option:selected').text();
                        console.log('KPI Selected in Modal:', { value: selectedValue, text: selectedText });

                        // Trigger weight info update if function exists
                        if (typeof updateWeightInfo === 'function') {
                            updateWeightInfo(selectedValue, null);
                        }
                    });

                } else {
                    console.warn('Bootstrap Select not available, using standard dropdown');
                }
            } catch (error) {
                console.error('Error initializing Bootstrap Select:', error);
            }

            document.getElementById('kpiModal').style.display = 'block';

            // Focus on category dropdown after modal opens
            setTimeout(() => {
                document.getElementById('kpiCategory').focus();
            }, 100);
        }
        
        // Edit KPI
        function editKPI(kpi) {
            document.getElementById('employeeKpiId').value = kpi.id;
            document.getElementById('kpiCategory').value = kpi.kpi.category_id;
            loadKPIsByCategory(kpi.kpi.category_id).then(() => {
                document.getElementById('kpiSelect').value = kpi.kpi.id;
                // Refresh Bootstrap Select after setting value
                try {
                    if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                        $('#kpiSelect').selectpicker('refresh');
                    }
                } catch (error) {
                    console.error('Error refreshing Bootstrap Select:', error);
                }
                // Update weight info excluding the current KPI being edited
                updateWeightInfo(kpi.kpi.category_id, kpi.id);
            });
            document.getElementById('kpiWeight').value = kpi.weight;
            document.getElementById('kpiMeasurement').value = kpi.measurement || '';

            document.getElementById('kpiModal').style.display = 'block';
        }
        
        // Confirm delete KPI
        function confirmDeleteKPI(kpi) {
            document.getElementById('deleteKpiName').textContent = kpi.kpi.name;
            document.getElementById('confirmDelete').setAttribute('data-id', kpi.id);
            
            document.getElementById('deleteModal').style.display = 'block';
        }

        // Validate category weights total 100%
        async function validateCategoryWeights(employeeCode, newKpiId, newWeight, employeeKpiId) {
            try {
                console.log('Validating weights:', { employeeCode, newKpiId, newWeight, employeeKpiId });

                // Get the category of the new/updated KPI
                const { data: kpiData, error: kpiError } = await supabaseClient
                    .from('kpis')
                    .select('category_id, name')
                    .eq('id', newKpiId)
                    .single();

                if (kpiError) {
                    console.error('Error fetching KPI data:', kpiError);
                    throw kpiError;
                }

                console.log('KPI data:', kpiData);

                // Get all current KPIs for this employee in the same category
                const { data: currentKpis, error: currentError } = await supabaseClient
                    .from('employee_kpis')
                    .select('id, weight, kpi_id, kpis!inner(category_id, name)')
                    .eq('employee_code', employeeCode)
                    .eq('kpis.category_id', kpiData.category_id);

                if (currentError) {
                    console.error('Error fetching current KPIs:', currentError);
                    throw currentError;
                }

                console.log('Current KPIs in category:', currentKpis);

                // Calculate total weight including the new/updated KPI
                let totalWeight = 0;
                let isUpdate = false;

                // First, add weights from existing KPIs (excluding the one being updated)
                currentKpis.forEach(kpi => {
                    if (employeeKpiId && kpi.id === employeeKpiId) {
                        // This is the KPI being updated - skip it, we'll add the new weight
                        isUpdate = true;
                    } else if (kpi.kpi_id === newKpiId && !employeeKpiId) {
                        // Duplicate KPI check - this shouldn't happen
                        console.warn('Duplicate KPI detected:', kpi);
                    } else {
                        totalWeight += parseFloat(kpi.weight);
                    }
                });

                // Add the new/updated weight
                totalWeight += newWeight;

                console.log('Calculated total weight:', totalWeight);

                // Get category name for error message
                const { data: categoryData, error: categoryError } = await supabaseClient
                    .from('kpi_categories')
                    .select('name')
                    .eq('id', kpiData.category_id)
                    .single();

                if (categoryError) {
                    console.error('Error fetching category data:', categoryError);
                    throw categoryError;
                }

                if (totalWeight !== 100) {
                    const message = `${categoryData.name} KPIs total weight will be ${totalWeight}%. Must equal 100%.`;
                    console.warn('Weight validation failed:', message);
                    appUtils.showNotification(message, 'error');
                    return false;
                }

                console.log('Weight validation passed');
                return true;

            } catch (error) {
                console.error('Error validating category weights:', error);
                appUtils.showNotification('Error validating weights', 'error');
                return false;
            }
        }

        // Assign KPI to employee
        async function assignKPI(employeeCode) {
            try {
                console.log('Starting KPI assignment for employee:', employeeCode);

                const employeeKpiId = document.getElementById('employeeKpiId').value;
                const kpiId = document.getElementById('kpiSelect').value;
                const weight = parseInt(document.getElementById('kpiWeight').value);
                const measurement = document.getElementById('kpiMeasurement').value.trim();

                console.log('Form values:', { employeeKpiId, kpiId, weight });

                if (!kpiId) {
                    appUtils.showNotification('Please select a KPI', 'error');
                    return;
                }

                if (!weight || weight <= 0 || weight > 100) {
                    appUtils.showNotification('Weight must be between 1 and 100', 'error');
                    return;
                }

                console.log('Proceeding with assignment (validation will be done when saving all changes)...');
                
                // Check if KPI is already assigned (for new assignments)
                if (!employeeKpiId) {
                    const { data: existing, error: checkError } = await supabaseClient
                        .from('employee_kpis')
                        .select('id')
                        .eq('employee_code', employeeCode)
                        .eq('kpi_id', kpiId);
                    
                    if (checkError) throw checkError;
                    
                    if (existing && existing.length > 0) {
                        appUtils.showNotification('This KPI is already assigned to the employee', 'error');
                        return;
                    }
                }
                
                // Update or insert
                if (employeeKpiId) {
                    // Update existing
                    const { error } = await supabaseClient
                        .from('employee_kpis')
                        .update({
                            kpi_id: kpiId,
                            weight: weight,
                            measurement: measurement
                        })
                        .eq('id', employeeKpiId);
                    
                    if (error) throw error;
                    
                    appUtils.showNotification('KPI updated successfully', 'success');
                } else {
                    // Insert new
                    const { error } = await supabaseClient
                        .from('employee_kpis')
                        .insert({
                            employee_code: employeeCode,
                            kpi_id: kpiId,
                            weight: weight,
                            measurement: measurement
                        });
                    
                    if (error) throw error;
                    
                    appUtils.showNotification('KPI assigned successfully', 'success');
                }
                
                // Close modal and reload
                closeModals();
                loadAssignedKPIs(employeeCode);
                
            } catch (error) {
                console.error('Error assigning KPI:', error);
                appUtils.showNotification('Error assigning KPI', 'error');
            }
        }
        
        // Remove KPI from employee
        async function removeKPI(kpiId, employeeCode) {
            try {
                const { error } = await supabaseClient
                    .from('employee_kpis')
                    .delete()
                    .eq('id', kpiId);
                
                if (error) throw error;
                
                appUtils.showNotification('KPI removed successfully', 'success');
                
                // Close modal and reload
                closeModals();
                loadAssignedKPIs(employeeCode);
                
            } catch (error) {
                console.error('Error removing KPI:', error);
                appUtils.showNotification('Error removing KPI', 'error');
            }
        }
        
        // Validate all KPI weights within categories total 100%
        async function validateAllCategories() {
            try {
                const employeeCode = getEmployeeCodeFromURL();
                console.log('Validating KPI weights for employee:', employeeCode);

                // Simplified query to get KPI assignments with category info
                const { data: assignedKpis, error: kpisError } = await supabaseClient
                    .from('employee_kpis')
                    .select(`
                        weight,
                        kpis (
                            name,
                            category_id
                        )
                    `)
                    .eq('employee_code', employeeCode);

                if (kpisError) {
                    console.error('Error fetching assigned KPIs:', kpisError);
                    throw kpisError;
                }

                console.log('Assigned KPIs:', assignedKpis);

                if (!assignedKpis || assignedKpis.length === 0) {
                    appUtils.showNotification('No KPIs assigned to this employee', 'warning');
                    alert('⚠️ No KPIs assigned to this employee yet.');
                    return;
                }

                // Get category names
                const { data: categories, error: categoriesError } = await supabaseClient
                    .from('kpi_categories')
                    .select('id, name');

                if (categoriesError) {
                    console.error('Error fetching categories:', categoriesError);
                    throw categoriesError;
                }

                // Create category lookup
                const categoryLookup = {};
                categories.forEach(cat => {
                    categoryLookup[cat.id] = cat.name;
                });

                // Group by category and calculate totals
                const categoryTotals = {};

                assignedKpis.forEach(kpi => {
                    if (kpi.kpis && kpi.kpis.category_id) {
                        const categoryName = categoryLookup[kpi.kpis.category_id];
                        if (categoryName) {
                            if (!categoryTotals[categoryName]) {
                                categoryTotals[categoryName] = 0;
                            }
                            categoryTotals[categoryName] += parseFloat(kpi.weight);
                        }
                    }
                });

                console.log('Category totals:', categoryTotals);

                let allValid = true;
                let validationResults = [];

                // Check each category
                Object.entries(categoryTotals).forEach(([categoryName, total]) => {
                    const roundedTotal = Math.round(total * 100) / 100; // Round to 2 decimal places
                    const isValid = Math.abs(roundedTotal - 100) < 0.01; // Allow for small floating point differences
                    validationResults.push({
                        category: categoryName,
                        total: roundedTotal,
                        valid: isValid
                    });

                    if (!isValid) {
                        allValid = false;
                    }
                });

                // Show validation results
                let message = 'KPI Weight Validation Results:\n\n';
                validationResults.forEach(result => {
                    const status = result.valid ? '✅' : '❌';
                    message += `${status} ${result.category} KPIs: ${result.total}%\n`;
                });

                if (allValid) {
                    message += '\n🎉 All KPI categories are properly weighted at 100%!';
                    appUtils.showNotification('All KPI weights are valid!', 'success');
                } else {
                    message += '\n⚠️ Please adjust KPI weights so each category totals 100%';
                    appUtils.showNotification('Some KPI categories do not total 100%', 'warning');
                }

                alert(message);

            } catch (error) {
                console.error('Error validating KPI weights:', error);
                console.error('Error details:', error.message);
                appUtils.showNotification('Error validating KPI weights: ' + error.message, 'error');
            }
        }

        // Close all modals
        function closeModals() {
            document.getElementById('kpiModal').style.display = 'none';
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Load current user name and show welcome message
        async function loadCurrentUserName() {
            try {
                const currentUser = appAuth.getCurrentUser();
                if (currentUser) {
                    // Update display name to show only first name or "Master Admin"
                    appUtils.updateUserDisplayName('currentUserName', {
                        role: 'admin',
                        name: currentUser.username || currentUser.name
                    });

                    // Show welcome message
                    appUtils.showWelcomeMessage({
                        role: 'admin',
                        name: currentUser.username || currentUser.name
                    });
                } else {
                    document.getElementById('currentUserName').textContent = 'Admin';
                }
            } catch (error) {
                console.error('Error loading user name:', error);
                document.getElementById('currentUserName').textContent = 'Admin';
            }
        }
    </script>
</body>
</html>